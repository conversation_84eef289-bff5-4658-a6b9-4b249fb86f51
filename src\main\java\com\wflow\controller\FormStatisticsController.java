package com.wflow.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.bean.entity.WflowFormStatisticsConfig;
import com.wflow.bean.vo.FormMenuVo;
import com.wflow.bean.vo.FormStatisticsVo;
import com.wflow.service.FormStatisticsService;
import com.wflow.utils.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 表单统计控制器
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@RestController
@RequestMapping("wflow/form/statistics")
@Tag(name = "表单统计管理")
public class FormStatisticsController {

    @Autowired
    private FormStatisticsService formStatisticsService;

    /**
     * 创建统计配置
     */
    @PostMapping("/config")
    @Operation(summary = "创建统计配置")
    public Object createStatisticsConfig(@RequestBody WflowFormStatisticsConfig config) {
        String configId = formStatisticsService.createStatisticsConfig(config);
        return R.ok("创建统计配置成功" + configId);
    }

    /**
     * 更新统计配置
     */
    @PutMapping("/config")
    @Operation(summary = "更新统计配置")
    public Object updateStatisticsConfig(@RequestBody WflowFormStatisticsConfig config) {
        boolean success = formStatisticsService.updateStatisticsConfig(config);
        return success ? R.ok("更新统计配置成功") : R.error("更新统计配置失败");
    }

    /**
     * 删除统计配置
     */
    @DeleteMapping("/config/{configId}")
    @Operation(summary = "删除统计配置")
    public Object deleteStatisticsConfig(@PathVariable String configId) {
        boolean success = formStatisticsService.deleteStatisticsConfig(configId);
        return success ? R.ok("删除统计配置成功") : R.error("删除统计配置失败");
    }

    /**
     * 启用/禁用统计配置
     */
    @PutMapping("/config/{configId}/toggle")
    @Operation(summary = "启用/禁用统计配置")
    public Object toggleStatisticsConfig(@PathVariable String configId,
            @RequestParam Integer status) {
        boolean success = formStatisticsService.toggleStatisticsConfig(configId, status);
        String action = status == 1 ? "启用" : "禁用";
        return success ? R.ok(action + "统计配置成功") : R.error(action + "统计配置失败");
    }

    /**
     * 分页查询统计配置
     */
    @GetMapping("/config/list")
    @Operation(summary = "分页查询统计配置")
    public Object getStatisticsConfigs(@RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestParam(required = false) String formId,
            @RequestParam(required = false) Integer status) {
        Page<WflowFormStatisticsConfig> page = formStatisticsService.getStatisticsConfigs(
                pageNo, pageSize, formId, status);
        return R.ok(page);
    }
    /*
     * 查询菜单配置
     */
    @GetMapping("/config/menu")
    @Operation(summary = "查询菜单配置")
    public Object getMenu() {
        List<FormMenuVo> list = formStatisticsService.getMenu();
        return R.ok(list);
    }

    /**
     * 根据表单ID获取统计配置
     */
    @GetMapping("/config/form/{formId}")
    @Operation(summary = "根据表单ID获取统计配置")
    public Object getStatisticsConfigsByFormId(@PathVariable String formId) {
        List<WflowFormStatisticsConfig> configs = formStatisticsService.getStatisticsConfigsByFormId(formId);
        return R.ok(configs);
    }
    /**
     * 根据表单ID获取统计配置
     */
    @GetMapping("/config/{type}")
    @Operation(summary = "根据类型获取统计配置")
    public Object getStatisticsConfigsByType(@PathVariable String type) {
        List<WflowFormStatisticsConfig> configs = formStatisticsService.getStatisticsConfigsByType(type);
        return R.ok(configs);
    }

    /**
     * 执行实时统计
     */
//    @GetMapping("/execute/{configId}")
//    @Operation(summary = "执行实时统计")
//    public Object executeRealTimeStatistics(@PathVariable String configId,
//            @RequestParam(defaultValue = "false") boolean includeFinished) {
//        FormStatisticsVo result = formStatisticsService.executeRealTimeStatistics(configId, includeFinished);
//        return R.ok(result);
//    }

    /**
     * 批量执行统计
     */
//    @PostMapping("/execute/batch")
//    @Operation(summary = "批量执行统计")
//    public Object executeBatchStatistics(@RequestBody List<String> configIds,
//            @RequestParam(defaultValue = "false") boolean includeFinished) {
//        List<FormStatisticsVo> results = formStatisticsService.executeBatchStatistics(configIds, includeFinished);
//        return R.ok(results);
//    }

    /**
     * 根据表单ID执行所有相关统计
     */
    @GetMapping("/execute/form/{formId}")
    @Operation(summary = "根据表单ID执行所有相关统计")
    public Object executeStatisticsByFormId(@PathVariable String formId,
            @RequestParam(defaultValue = "false") boolean includeFinished) {
        List<FormStatisticsVo> results = formStatisticsService.executeStatisticsByFormId(formId, includeFinished);
        return R.ok(results);
    }

    /**
     * 获取表单字段的所有可能值
     */
    @GetMapping("/field/values")
    @Operation(summary = "获取表单字段的所有可能值")
    public Object getFieldDistinctValues(@RequestParam String formId,
            @RequestParam String fieldId,
            @RequestParam(defaultValue = "50") Integer limit) {
        List<String> values = formStatisticsService.getFieldDistinctValues(formId, fieldId, limit);
        return R.ok(values);
    }

    /**
     * 刷新统计缓存
     */
//    @PostMapping("/cache/refresh/{configId}")
//    @Operation(summary = "刷新统计缓存")
//    public Object refreshStatisticsCache(@PathVariable String configId) {
//        boolean success = formStatisticsService.refreshStatisticsCache(configId);
//        return success ? R.ok("刷新统计缓存成功") : R.error("刷新统计缓存失败");
//    }

    /**
     * 获取统计概览（仪表板用）
     */
//    @GetMapping("/overview")
//    @Operation(summary = "获取统计概览")
//    public Object getStatisticsOverview(@RequestParam(required = false) String formId,
//            @RequestParam(defaultValue = "false") boolean includeFinished) {
//        if (formId != null) {
//            List<FormStatisticsVo> results = formStatisticsService.executeStatisticsByFormId(formId, includeFinished);
//            return R.ok(results);
//        } else {
//            // 获取所有启用的配置并执行统计
//            Page<WflowFormStatisticsConfig> configs = formStatisticsService.getStatisticsConfigs(1, 100, null, 1);
//            List<String> configIds = configs.getRecords().stream()
//                    .map(WflowFormStatisticsConfig::getId)
//                    .toList();
//            List<FormStatisticsVo> results = formStatisticsService.executeBatchStatistics(configIds, includeFinished);
//            return R.ok(results);
//        }
//    }
    @GetMapping("/home")
    @Operation(summary = "获取首页统计")
    public Object getstatistics(@RequestParam String type){
        return R.ok(formStatisticsService.getstatistics(type));
    }
    @GetMapping("/xzpStatistics")
    @Operation(summary = "根据行政区统计")
    public Object getXzpStatistics(@RequestParam(defaultValue = "140000000000") String xzqCode,@RequestParam(required = false) String fieldId,@RequestParam String formId) {
        return  R.ok(formStatisticsService.getXzpStatistics(xzqCode, fieldId,formId));
    }
//    @GetMapping("/tj")
//    @Operation(summary = "统计")
//    public Object tj(@RequestParam String xzqCode,@RequestParam(required = false) String id) {
//        return  R.ok(formStatisticsService.calculateFieldSum(xzqCode, id,true));
//    }
}
