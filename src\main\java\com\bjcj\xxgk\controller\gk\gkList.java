package com.bjcj.xxgk.controller.gk;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.model.pojo.gk.GkList;
import com.bjcj.xxgk.serviceImpl.gk.GkListService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 征地信息公开列表控制器
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@SaCheckLogin
@RestController
@RequestMapping("/gk/list")
@Tag(name = "征地信息公开列表")
@Validated
public class gkList {

    @Resource
    private GkListService gkListService;

    @Operation(summary = "分页查询征地信息公开列表", description = "支持按行政区编码、所属区、街道、单位、批准文号等条件分页查询征地信息公开列表")
    @ApiOperationSupport(order = 1)
    @GetMapping
    public JsonResult pageList(
            @Parameter(description = "页码", required = true, example = "1") @RequestParam("pageNum") Integer pageNum,

            @Parameter(description = "每页大小", required = true, example = "10") @RequestParam("pageSize") Integer pageSize,

            @Parameter(description = "行政区编码（对应SysXzq14表中的code字段）", example = "140000") @RequestParam(value = "districtCode", required = false) String districtCode,

            @Parameter(description = "所属区（精确匹配）", example = "小店区") @RequestParam(value = "district", required = false) String district,

            @Parameter(description = "街道（精确匹配）", example = "坞城街道") @RequestParam(value = "street", required = false) String street,

            @Parameter(description = "单位（精确匹配）", example = "太原市小店区政府") @RequestParam(value = "unit", required = false) String unit,

            @Parameter(description = "批准文号（精确匹配）", example = "晋政征[2024]001号") @RequestParam(value = "approvalNumber", required = false) String approvalNumber,
            @Parameter(description = "业务类型", example = "征地") @RequestParam(value = "type", required = false) String type
            ) {
        // 参数校验
        if (pageNum == null || pageNum < 1) {
            return JsonResult.error("页码必须大于0");
        }
        if (pageSize == null || pageSize < 1 || pageSize > 100) {
            return JsonResult.error("每页大小必须在1-100之间");
        }

        // 如果提供了行政区编码，先验证其是否存在
        if (StrUtil.isNotBlank(districtCode)) {
            if (!gkListService.isDistrictCodeExists(districtCode)) {
                log.warn("查询使用了不存在的行政区编码: {}", districtCode);
                return JsonResult.error("行政区编码不存在: " + districtCode);
            }
        }

        // 创建分页对象
        Page<GkList> page = new Page<>(pageNum, pageSize);

        // 执行查询
        return gkListService.pageList(page, districtCode, district, street, unit, approvalNumber,type);
    }

    @Operation(summary = "验证行政区编码", description = "验证指定的行政区编码是否存在")
    @ApiOperationSupport(order = 2)
    @GetMapping("/validate-district-code")
    public JsonResult validateDistrictCode(
            @Parameter(description = "行政区编码", required = true, example = "140000") @RequestParam("districtCode") String districtCode) {
        if (StrUtil.isBlank(districtCode)) {
            return JsonResult.error("行政区编码不能为空");
        }

        boolean exists = gkListService.isDistrictCodeExists(districtCode);
        return JsonResult.success(exists);
    }
}
