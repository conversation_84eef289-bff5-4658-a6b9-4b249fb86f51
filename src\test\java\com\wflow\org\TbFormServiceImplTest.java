package com.wflow.org;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wflow.bean.entity.WflowFormData;
import com.wflow.mapper.*;
import com.wflow.workflow.bean.process.form.Form;
import com.wflow.workflow.service.impl.TbFormServiceImpl;
import com.wflow.workflow.service.impl.VarFormServiceImpl;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TbFormServiceImplTest {

    @InjectMocks
    private TbFormServiceImpl tbFormService;

    @Mock
    private WflowFormDataMapper formDataMapper;

    @Mock
    private RuntimeService runtimeService;

    @Mock
    private VarFormServiceImpl varFormService;

    @Mock
    private WflowModelsMapper modelsMapper;

    @Mock
    private WflowModelHistorysMapper historysMapper;

    @Mock
    private WflowNodeFormInfoMapper nodeFormInfoMapper;

    @Mock
    private WflowFormInfoMapper formInfoMapper;

    private ProcessInstance mockProcessInstance;
    private List<WflowFormData> mockFormDataList;

    @BeforeEach
    void setUp() {
        mockProcessInstance = mock(ProcessInstance.class);
        mockFormDataList = new ArrayList<>();
    }

    @Test
    void getProcessInstanceFormData_ShouldReturnFormData() {
        String instanceId = "testInstanceId";
        when(formDataMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(mockFormDataList);

        Map<String, Object> result = tbFormService.getProcessInstanceFormData(instanceId);

        assertNotNull(result);
        assertEquals(mockFormDataList.size(), result.size());
    }




}
