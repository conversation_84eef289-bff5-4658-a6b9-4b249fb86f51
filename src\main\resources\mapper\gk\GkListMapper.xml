<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.gk.GkListMapper">
    
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.gk.GkList">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="instance_id" jdbcType="VARCHAR" property="instanceId"/>
        <result column="district" jdbcType="VARCHAR" property="district"/>
        <result column="street" jdbcType="VARCHAR" property="street"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="approval_year" jdbcType="VARCHAR" property="approvalYear"/>
        <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber"/>
        <result column="gk_time" jdbcType="VARCHAR" property="gkTime"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, instance_id, district, street, unit, project_name, approval_year, approval_number, gk_time, type
    </sql>

    <!-- 根据行政区编码列表分页查询征地信息公开列表 -->
    <select id="selectPageByDistrictCodes" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM gk_list gl
        <where>
            <if test="districtCodes != null and districtCodes.size() > 0">
                AND EXISTS (
                    SELECT 1 FROM sys_xzq14 sx
                    WHERE sx.code IN
                    <foreach collection="districtCodes" item="code" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                    AND gl.district = sx.name
                )
            </if>
            <if test="district != null and district != ''">
                AND gl.district = #{district}
            </if>
            <if test="street != null and street != ''">
                AND gl.street = #{street}
            </if>
            <if test="unit != null and unit != ''">
                AND gl.unit = #{unit}
            </if>
            <if test="approvalNumber != null and approvalNumber != ''">
                AND gl.approval_number = #{approvalNumber}
            </if>
             <if test="type != null and type != ''">
                AND gl.type = #{type}
            </if>
        </where>
        ORDER BY gl.gk_time DESC, gl.approval_year DESC, gl.id DESC
    </select>

</mapper>
