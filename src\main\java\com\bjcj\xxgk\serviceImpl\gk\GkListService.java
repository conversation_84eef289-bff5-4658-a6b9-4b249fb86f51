package com.bjcj.xxgk.serviceImpl.gk;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.mapper.gk.GkListMapper;
import com.bjcj.xxgk.mapper.sys.SysXzq14Mapper;
import com.bjcj.xxgk.model.pojo.gk.GkList;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 征地信息公开列表服务类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class GkListService extends ServiceImpl<GkListMapper, GkList> {

    @Resource
    private GkListMapper gkListMapper;

    @Resource
    private SysXzq14Mapper sysXzq14Mapper;

    /**
     * 分页查询征地信息公开列表
     * 
     * @param page           分页参数
     * @param districtCode   行政区编码（可选）
     * @param district       所属区（可选）
     * @param street         街道（可选）
     * @param unit           单位（可选）
     * @param approvalNumber 批准文号（可选）
     * @return 分页结果
     */
    public JsonResult pageList(Page<GkList> page, String districtCode, String district,
            String street, String unit, String approvalNumber,String type) {
        try {
            List<String> districtCodes = null;

            // 如果提供了行政区编码，则查询所有子级行政区编码
            if (StrUtil.isNotBlank(districtCode)) {
                districtCodes = getDistrictCodesWithChildren(districtCode);
                if (districtCodes.isEmpty()) {
                    log.warn("未找到行政区编码: {}", districtCode);
                    // 如果行政区编码不存在，返回空结果
                    return JsonResult.success(new Page<GkList>(page.getCurrent(), page.getSize()));
                }
            }

            // 执行分页查询
            IPage<GkList> result = gkListMapper.selectPageByDistrictCodes(
                    page, districtCodes, district, street, unit, approvalNumber,type);

            return JsonResult.success(result);

        } catch (Exception e) {
            log.error("查询征地信息公开列表失败", e);
            return JsonResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定行政区编码及其所有子级行政区编码
     * 
     * @param districtCode 行政区编码
     * @return 行政区编码列表
     */
    private List<String> getDistrictCodesWithChildren(String districtCode) {
        try {
            List<String> codes = sysXzq14Mapper.getChildrenCodesByCode(districtCode);
            return codes != null ? codes : Collections.emptyList();
        } catch (Exception e) {
            log.error("查询行政区子级编码失败，districtCode: {}", districtCode, e);
            return Collections.emptyList();
        }
    }

    /**
     * 验证行政区编码是否存在
     * 
     * @param districtCode 行政区编码
     * @return 是否存在
     */
    public boolean isDistrictCodeExists(String districtCode) {
        if (StrUtil.isBlank(districtCode)) {
            return false;
        }
        try {
            List<String> codes = sysXzq14Mapper.getChildrenCodesByCode(districtCode);
            return codes != null && !codes.isEmpty();
        } catch (Exception e) {
            log.error("验证行政区编码失败，districtCode: {}", districtCode, e);
            return false;
        }
    }
}
