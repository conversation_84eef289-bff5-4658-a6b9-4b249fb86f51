package com.wflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wflow.bean.entity.WflowFormStatisticsConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 表单字段统计配置Mapper
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Mapper
public interface WflowFormStatisticsConfigMapper extends BaseMapper<WflowFormStatisticsConfig> {

    /**
     * 根据表单ID查询启用的统计配置
     * @param formId 表单ID
     * @return 统计配置列表
     */
    @Select("SELECT * FROM wflow_form_statistics_config WHERE form_id = #{formId} AND status = 1 ORDER BY create_time DESC")
    List<WflowFormStatisticsConfig> selectEnabledByFormId(@Param("formId") String formId);

    /**
     * 根据字段ID查询启用的统计配置
     * @param fieldId 字段ID
     * @return 统计配置列表
     */
    @Select("SELECT * FROM wflow_form_statistics_config WHERE field_id = #{fieldId} AND status = 1")
    List<WflowFormStatisticsConfig> selectEnabledByFieldId(@Param("fieldId") String fieldId);
}
