package com.wflow.workflow.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsHomeVo {
    @Schema(description = "报件总数")
    private Long total;
    @Schema(description = "流程节点统计")
    private List<LinkVo> linkVos;
    @Schema(description = "表单内容统计")
    private List<FormInsideVo> formInsideVos;
}
