<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/test">
  <process id="wf685b9efff6447f2e14e18bd0" name="请假" isExecutable="true">
    <startEvent id="start" name="开始"></startEvent>
    <userTask id="root" name="发起人" flowable:assignee="${initiator}">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${userTaskListener}"></flowable:executionListener>
      </extensionElements>
    </userTask>
    <sequenceFlow id="start_root" sourceRef="start" targetRef="root"></sequenceFlow>
    <userTask id="node_349331107707" name="审批人" flowable:assignee="${assignee}">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${userTaskListener}"></flowable:executionListener>
        <flowable:taskListener event="all" delegateExpression="${userTaskListener}"></flowable:taskListener>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${processTaskService.getNodeApprovalUsers(execution)}" flowable:elementVariable="assignee">
        <completionCondition>${uelTools.nodeIsComplete(execution, 'AND')}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <sequenceFlow id="root_node_349331107707" sourceRef="root" targetRef="node_349331107707"></sequenceFlow>
    <sequenceFlow id="node_349331107707_process-end" sourceRef="node_349331107707" targetRef="process-end"></sequenceFlow>
    <endEvent id="process-end" name="审批流程结束"></endEvent>
    <endEvent id="cancel-end" name="审批流程撤消">
      <terminateEventDefinition flowable:terminateAll="true"></terminateEventDefinition>
    </endEvent>
    <endEvent id="refuse-end" name="审批流程被驳回">
      <terminateEventDefinition flowable:terminateAll="true"></terminateEventDefinition>
    </endEvent>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_wf685b9efff6447f2e14e18bd0">
    <bpmndi:BPMNPlane bpmnElement="wf685b9efff6447f2e14e18bd0" id="BPMNPlane_wf685b9efff6447f2e14e18bd0">
      <bpmndi:BPMNShape bpmnElement="node_349331107707" id="BPMNShape_node_349331107707">
        <omgdc:Bounds height="60.0" width="100.0" x="230.0" y="0.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="refuse-end" id="BPMNShape_refuse-end">
        <omgdc:Bounds height="30.0" width="30.0" x="0.0" y="210.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="root" id="BPMNShape_root">
        <omgdc:Bounds height="60.0" width="100.0" x="80.0" y="0.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="start" id="BPMNShape_start">
        <omgdc:Bounds height="30.0" width="30.0" x="0.0" y="15.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="cancel-end" id="BPMNShape_cancel-end">
        <omgdc:Bounds height="30.0" width="30.0" x="0.0" y="120.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="process-end" id="BPMNShape_process-end">
        <omgdc:Bounds height="30.0" width="30.0" x="380.0" y="15.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="start_root" id="BPMNEdge_start_root">
        <omgdi:waypoint x="30.0" y="30.0"></omgdi:waypoint>
        <omgdi:waypoint x="42.0" y="30.0"></omgdi:waypoint>
        <omgdi:waypoint x="42.0" y="30.000000000000007"></omgdi:waypoint>
        <omgdi:waypoint x="80.0" y="30.000000000000007"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="node_349331107707_process-end" id="BPMNEdge_node_349331107707_process-end">
        <omgdi:waypoint x="330.0" y="30.0"></omgdi:waypoint>
        <omgdi:waypoint x="342.0" y="30.0"></omgdi:waypoint>
        <omgdi:waypoint x="342.0" y="30.000000000000004"></omgdi:waypoint>
        <omgdi:waypoint x="380.0" y="30.000000000000004"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="root_node_349331107707" id="BPMNEdge_root_node_349331107707">
        <omgdi:waypoint x="180.0" y="30.0"></omgdi:waypoint>
        <omgdi:waypoint x="192.0" y="30.0"></omgdi:waypoint>
        <omgdi:waypoint x="192.0" y="30.000000000000007"></omgdi:waypoint>
        <omgdi:waypoint x="230.0" y="30.000000000000007"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>