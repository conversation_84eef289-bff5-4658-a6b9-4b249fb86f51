package com.wflow.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 表单统计结果VO
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "表单统计结果")
public class FormStatisticsVo {

    @Schema(description = "统计配置ID")
    private String configId;

    @Schema(description = "统计配置名称")
    private String configName;

    @Schema(description = "表单ID")
    private String formId;

    @Schema(description = "表单名称")
    private String formName;

    @Schema(description = "字段ID")
    private String fieldId;

    @Schema(description = "字段名称")
    private String fieldName;

    @Schema(description = "字段key")
    private String fieldKey;

    @Schema(description = "目标值列表")
    private List<String> targetValues;

    @Schema(description = "匹配类型")
    private String matchType;

    @Schema(description = "统计详情")
    private List<StatisticsDetail> details;

    @Schema(description = "总匹配数量")
    private BigDecimal totalMatchCount;

    @Schema(description = "总流程实例数量")
    private Integer totalInstanceCount;

    @Schema(description = "总匹配率(%)")
    private BigDecimal totalMatchRate;

    @Schema(description = "统计时间")
    private LocalDateTime statisticsTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "统计详情")
    public static class StatisticsDetail {
        
        @Schema(description = "目标值")
        private String targetValue;

        @Schema(description = "匹配数量")
        private Integer matchCount;

        @Schema(description = "匹配率(%)")
        private BigDecimal matchRate;

        @Schema(description = "匹配的流程实例列表")
        private List<MatchedInstance> matchedInstances;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "匹配的流程实例")
    public static class MatchedInstance {
        
        @Schema(description = "流程实例ID")
        private String instanceId;

        @Schema(description = "流程实例名称")
        private String instanceName;

        @Schema(description = "发起人")
        private String startUser;

        @Schema(description = "发起人姓名")
        private String startUserName;

        @Schema(description = "字段值")
        private String fieldValue;

        @Schema(description = "创建时间")
        private LocalDateTime createTime;

        @Schema(description = "流程状态")
        private String status;
    }
}
