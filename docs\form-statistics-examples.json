{"examples": [{"name": "员工年龄分布统计", "description": "统计员工年龄在不同区间的分布情况", "config": {"configName": "员工年龄分布统计", "formId": "employee_registration_form", "fieldId": "age", "fieldName": "年龄", "fieldKey": "age", "targetValue": "[\"20-30\", \"31-40\", \"41-50\", \"51-60\"]", "matchType": "RANGE"}, "expectedResult": {"details": [{"targetValue": "20-30", "matchCount": 45, "matchRate": 37.5}, {"targetValue": "31-40", "matchCount": 38, "matchRate": 31.67}]}}, {"name": "部门分布统计", "description": "统计员工在各个部门的分布情况", "config": {"configName": "部门分布统计", "formId": "employee_registration_form", "fieldId": "department", "fieldName": "部门", "fieldKey": "department", "targetValue": "[\"技术部\", \"销售部\", \"人事部\", \"财务部\", \"市场部\"]", "matchType": "EXACT"}}, {"name": "薪资水平统计", "description": "统计薪资高于某个标准的员工数量", "config": {"configName": "高薪员工统计", "formId": "salary_application_form", "fieldId": "expected_salary", "fieldName": "期望薪资", "fieldKey": "expectedSalary", "targetValue": "10000", "matchType": "GREATER_THAN"}}, {"name": "学历分布统计", "description": "统计员工学历分布情况", "config": {"configName": "学历分布统计", "formId": "employee_registration_form", "fieldId": "education", "fieldName": "学历", "fieldKey": "education", "targetValue": "[\"本科\", \"硕士\", \"博士\", \"专科\"]", "matchType": "EXACT"}}, {"name": "工作经验统计", "description": "统计不同工作经验年限的员工分布", "config": {"configName": "工作经验统计", "formId": "employee_registration_form", "fieldId": "work_experience", "fieldName": "工作经验", "fieldKey": "workExperience", "targetValue": "[\"0-2\", \"3-5\", \"6-10\", \"10+\"]", "matchType": "RANGE"}}, {"name": "地区分布统计", "description": "统计员工来源地区分布（包含匹配）", "config": {"configName": "地区分布统计", "formId": "employee_registration_form", "fieldId": "hometown", "fieldName": "籍贯", "fieldKey": "hometown", "targetValue": "[\"北京\", \"上海\", \"广州\", \"深圳\", \"杭州\"]", "matchType": "CONTAINS"}}, {"name": "技能标签统计", "description": "统计具有特定技能的员工数量", "config": {"configName": "Java技能统计", "formId": "developer_registration_form", "fieldId": "skills", "fieldName": "技能标签", "fieldKey": "skills", "targetValue": "Java", "matchType": "CONTAINS"}}, {"name": "项目预算统计", "description": "统计项目预算在不同范围的分布", "config": {"configName": "项目预算分布", "formId": "project_application_form", "fieldId": "budget", "fieldName": "项目预算", "fieldKey": "budget", "targetValue": "[\"0-50000\", \"50001-100000\", \"100001-500000\", \"500000+\"]", "matchType": "RANGE"}}, {"name": "客户满意度统计", "description": "统计客户满意度评分分布", "config": {"configName": "客户满意度统计", "formId": "customer_feedback_form", "fieldId": "satisfaction_score", "fieldName": "满意度评分", "fieldKey": "satisfactionScore", "targetValue": "[\"1-2\", \"3-4\", \"5-6\", \"7-8\", \"9-10\"]", "matchType": "RANGE"}}, {"name": "产品类型统计", "description": "统计不同产品类型的申请数量", "config": {"configName": "产品类型分布", "formId": "product_application_form", "fieldId": "product_type", "fieldName": "产品类型", "fieldKey": "productType", "targetValue": "[\"软件产品\", \"硬件产品\", \"服务产品\", \"解决方案\"]", "matchType": "EXACT"}}], "batchStatisticsExample": {"description": "批量执行多个统计配置", "request": {"configIds": ["config_age_distribution", "config_department_distribution", "config_salary_statistics", "config_education_distribution"], "includeFinished": false}, "usage": "POST /wflow/form/statistics/execute/batch?includeFinished=false"}, "formStatisticsExample": {"description": "根据表单ID执行所有相关统计", "request": {"formId": "employee_registration_form", "includeFinished": false}, "usage": "GET /wflow/form/statistics/execute/form/employee_registration_form?includeFinished=false"}, "realTimeStatisticsExample": {"description": "执行实时统计（仅统计运行中的流程）", "request": {"configId": "config_age_distribution", "includeFinished": false}, "usage": "GET /wflow/form/statistics/execute/config_age_distribution?includeFinished=false"}, "fieldValuesExample": {"description": "获取表单字段的所有可能值（用于配置参考）", "request": {"formId": "employee_registration_form", "fieldId": "department", "limit": 20}, "usage": "GET /wflow/form/statistics/field/values?formId=employee_registration_form&fieldId=department&limit=20", "expectedResponse": ["技术部", "销售部", "人事部", "财务部", "市场部", "运营部"]}, "matchTypeExamples": {"EXACT": {"description": "精确匹配", "example": "targetValue: '北京', fieldValue: '北京' -> 匹配"}, "CONTAINS": {"description": "包含匹配", "example": "targetValue: '北京', fieldValue: '北京市朝阳区' -> 匹配"}, "REGEX": {"description": "正则表达式匹配", "example": "targetValue: '\\d{4}', fieldValue: '2024' -> 匹配"}, "GREATER_THAN": {"description": "大于比较", "example": "targetValue: '5000', fieldValue: '8000' -> 匹配"}, "LESS_THAN": {"description": "小于比较", "example": "targetValue: '10000', fieldValue: '8000' -> 匹配"}, "NOT_EQUALS": {"description": "不等于", "example": "targetValue: '北京', fieldValue: '上海' -> 匹配"}, "RANGE": {"description": "范围匹配", "example": "targetValue: '20-30', fieldValue: '25' -> 匹配"}}, "performanceTips": ["对于频繁查询的统计配置，建议启用缓存机制", "避免统计过大范围的数据，建议按时间段分批统计", "确保表单数据表在相关字段上有适当的索引", "对于实时性要求不高的统计，可以定时刷新缓存", "使用批量统计接口可以减少网络请求次数"]}