package com.wflow.workflow.service.impl;

import com.wflow.workflow.bean.jumpback.JumpBackConfig;
import com.wflow.workflow.bean.jumpback.JumpBackResult;
import com.wflow.workflow.service.JumpBackConfigService;
import com.wflow.workflow.service.JumpBackExecutionService;
import com.wflow.workflow.service.OrgOwnershipService;
import com.wflow.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 流程回跳执行服务实现
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/23
 */
@Slf4j
@Service
public class JumpBackExecutionServiceImpl implements JumpBackExecutionService {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private TaskService taskService;

    @Autowired
    @Lazy
    private JumpBackConfigService jumpBackConfigService;

    @Autowired
    private OrgOwnershipService orgOwnershipService;

    // 回跳状态缓存，用于跟踪回跳次数和状态
    private final Map<String, JumpBackState> jumpBackStateCache = new ConcurrentHashMap<>();

    @Override
    public boolean checkJumpBackCondition(String processInstanceId, String nodeId) {
        try {
            log.debug("检查回跳条件: 流程={}, 节点={}", processInstanceId, nodeId);

            // 检查流程实例是否存在
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (processInstance == null) {
                // 检查历史流程实例
                HistoricProcessInstance historicInstance = historyService.createHistoricProcessInstanceQuery()
                        .processInstanceId(processInstanceId)
                        .singleResult();

                if (historicInstance == null) {
                    log.warn("流程实例不存在: {}", processInstanceId);
                    return false;
                }

                // 流程已结束，检查是否满足回跳条件
                return checkCompletedProcessJumpCondition(processInstanceId, nodeId);
            }

            // 流程正在运行，检查当前状态
            return checkRunningProcessJumpCondition(processInstanceId, nodeId);

        } catch (Exception e) {
            log.error("检查回跳条件时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JumpBackResult executeJumpBack(String processInstanceId, String configId) {
        return executeJumpBack(processInstanceId, configId, "系统自动触发");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JumpBackResult executeJumpBack(String processInstanceId, String configId, String reason) {
        long startTime = System.currentTimeMillis();
        log.info("开始执行回跳操作: 流程={}, 配置={}, 原因={}", processInstanceId, configId, reason);

        try {
            // 1. 获取回跳配置
            JumpBackConfig config = jumpBackConfigService.getJumpBackConfigsByProcessDef(null)
                    .stream()
                    .filter(c -> c.getId().equals(configId))
                    .findFirst()
                    .orElse(null);

            if (config == null) {
                return JumpBackResult.failure(processInstanceId, configId, "回跳配置不存在");
            }

            if (!config.getEnabled()) {
                return JumpBackResult.failure(processInstanceId, configId, "回跳配置已禁用");
            }

            // 2. 检查回跳条件
            if (!canJumpBack(processInstanceId, configId)) {
                return JumpBackResult.failure(processInstanceId, configId, "不满足回跳条件");
            }

            // 3. 检查回跳次数限制
            int currentJumpCount = getCurrentJumpCount(processInstanceId, configId);
            if (currentJumpCount >= config.getMaxJumpCount()) {
                return JumpBackResult.failure(processInstanceId, configId,
                        "已达到最大回跳次数限制: " + config.getMaxJumpCount());
            }

            // 4. 保存回跳前的变量状态
            Map<String, Object> variablesBefore = getCurrentProcessVariables(processInstanceId);

            // 5. 执行回跳操作
            JumpBackResult result = performJumpBack(processInstanceId, config, reason, variablesBefore);

            // 6. 更新回跳状态
            updateJumpBackState(processInstanceId, configId, currentJumpCount + 1);

            // 7. 记录执行时间
            long executionTime = System.currentTimeMillis() - startTime;
            result.setExecutionDuration(executionTime);

            log.info("回跳操作执行完成: 流程={}, 配置={}, 结果={}, 耗时={}ms",
                    processInstanceId, configId, result.getSuccess(), executionTime);

            return result;

        } catch (Exception e) {
            log.error("执行回跳操作时发生异常: {}", e.getMessage(), e);
            return JumpBackResult.failure(processInstanceId, configId, "执行回跳时发生异常: " + e.getMessage());
        }
    }

    @Override
    public List<JumpBackResult> getJumpBackHistory(String processInstanceId) {
        // 这里应该从数据库查询回跳历史记录
        // 暂时返回空列表，实际实现需要查询 wflow_jump_back_history 表
        return new ArrayList<>();
    }

    @Override
    public boolean cancelPendingJumpBack(String processInstanceId, String configId) {
        try {
            log.info("取消待执行的回跳: 流程={}, 配置={}", processInstanceId, configId);

            // 从缓存中移除回跳状态
            String stateKey = processInstanceId + "_" + configId;
            jumpBackStateCache.remove(stateKey);

            // 这里可以添加更多的取消逻辑，比如取消定时任务等

            return true;
        } catch (Exception e) {
            log.error("取消回跳操作时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean canJumpBack(String processInstanceId, String configId) {
        try {
            // 检查流程实例状态
            HistoricProcessInstance historicInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (historicInstance == null) {
                return false;
            }

            // 检查回跳次数限制
            int currentJumpCount = getCurrentJumpCount(processInstanceId, configId);
            JumpBackConfig config = jumpBackConfigService.getJumpBackConfigsByProcessDef(null)
                    .stream()
                    .filter(c -> c.getId().equals(configId))
                    .findFirst()
                    .orElse(null);

            if (config == null || currentJumpCount >= config.getMaxJumpCount()) {
                return false;
            }

            // 检查目标节点是否有效
            return isTargetNodeValid(processInstanceId, config.getTargetNodeId());

        } catch (Exception e) {
            log.error("检查是否可以回跳时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int getCurrentJumpCount(String processInstanceId, String configId) {
        String stateKey = processInstanceId + "_" + configId;
        JumpBackState state = jumpBackStateCache.get(stateKey);
        return state != null ? state.getJumpCount() : 0;
    }

    @Override
    public boolean resetJumpBackState(String processInstanceId, String configId) {
        try {
            String stateKey = processInstanceId + "_" + configId;
            jumpBackStateCache.remove(stateKey);
            log.info("重置回跳状态: 流程={}, 配置={}", processInstanceId, configId);
            return true;
        } catch (Exception e) {
            log.error("重置回跳状态时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查已完成流程的回跳条件
     */
    private boolean checkCompletedProcessJumpCondition(String processInstanceId, String nodeId) {
        try {
            // 检查流程是否正常结束
            HistoricProcessInstance historicInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .finished()
                    .singleResult();

            if (historicInstance == null) {
                return false;
            }

            // 检查指定节点是否已执行
            List<HistoricActivityInstance> activities = historyService.createHistoricActivityInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .activityId(nodeId)
                    .finished()
                    .list();

            return !activities.isEmpty();

        } catch (Exception e) {
            log.error("检查已完成流程回跳条件时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查运行中流程的回跳条件
     */
    private boolean checkRunningProcessJumpCondition(String processInstanceId, String nodeId) {
        try {
            // 检查当前活动节点
            List<Execution> executions = runtimeService.createExecutionQuery()
                    .processInstanceId(processInstanceId)
                    .activityId(nodeId)
                    .list();

            // 如果指定节点正在执行，则不满足回跳条件
            if (!executions.isEmpty()) {
                return false;
            }

            // 检查是否有活动的任务
            List<Task> activeTasks = taskService.createTaskQuery()
                    .processInstanceId(processInstanceId)
                    .active()
                    .list();

            // 根据具体业务逻辑判断是否满足回跳条件
            return activeTasks.isEmpty();

        } catch (Exception e) {
            log.error("检查运行中流程回跳条件时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 执行具体的回跳操作
     */
    private JumpBackResult performJumpBack(String processInstanceId, JumpBackConfig config,
            String reason, Map<String, Object> variablesBefore) {
        try {
            log.info("执行回跳操作: 从节点 {} 回跳到节点 {}", config.getSourceNodeId(), config.getTargetNodeId());

            // 1. 检查目标节点是否有效
            if (!isTargetNodeValid(processInstanceId, config.getTargetNodeId())) {
                return JumpBackResult.failure(processInstanceId, config.getId(), "目标节点无效: " + config.getTargetNodeId());
            }

            // 2. 处理流程变量
            Map<String, Object> variablesAfter = processVariables(processInstanceId, config.getDataMapping(),
                    variablesBefore);

            // 3. 执行回跳
            boolean jumpSuccess = executeActualJumpBack(processInstanceId, config.getTargetNodeId());

            if (!jumpSuccess) {
                return JumpBackResult.failure(processInstanceId, config.getId(), "回跳执行失败");
            }

            // 4. 创建成功结果
            JumpBackResult result = JumpBackResult.success(
                    processInstanceId,
                    config.getId(),
                    config.getSourceNodeId(),
                    config.getTargetNodeId(),
                    getCurrentJumpCount(processInstanceId, config.getId()) + 1);

            result.setJumpReason(reason);
            result.setVariablesBefore(variablesBefore);
            result.setVariablesAfter(variablesAfter);
            try {
                result.setExecuteBy(UserUtil.getLoginUserId());
            } catch (Exception e) {
                result.setExecuteBy("system");
            }

            return result;

        } catch (Exception e) {
            log.error("执行回跳操作时发生异常: {}", e.getMessage(), e);
            return JumpBackResult.failure(processInstanceId, config.getId(), "回跳执行异常: " + e.getMessage());
        }
    }

    /**
     * 执行实际的回跳操作
     */
    private boolean executeActualJumpBack(String processInstanceId, String targetNodeId) {
        try {
            // 检查流程实例是否存在且正在运行
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (processInstance == null) {
                // 流程已结束，需要重新启动到指定节点
                return restartProcessToNode(processInstanceId, targetNodeId);
            } else {
                // 流程正在运行，跳转到指定节点
                return jumpToNode(processInstanceId, targetNodeId);
            }

        } catch (Exception e) {
            log.error("执行实际回跳操作时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 重新启动流程到指定节点
     */
    private boolean restartProcessToNode(String processInstanceId, String targetNodeId) {
        try {
            log.info("重新启动流程到指定节点: 流程={}, 目标节点={}", processInstanceId, targetNodeId);

            // 获取历史流程实例信息
            HistoricProcessInstance historicInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (historicInstance == null) {
                log.error("历史流程实例不存在: {}", processInstanceId);
                return false;
            }

            // 获取流程变量
            Map<String, Object> variables = getCurrentProcessVariables(processInstanceId);

            // 使用流程重启功能
            // 注意：这里使用简化的实现，实际项目中可能需要更复杂的逻辑
            try {
                // 尝试使用 Flowable 的流程操作 API
                ProcessInstance newInstance = runtimeService.startProcessInstanceById(
                        historicInstance.getProcessDefinitionId(),
                        processInstanceId,
                        variables);

                if (newInstance != null) {
                    log.info("流程重新启动成功: 流程={}, 目标节点={}", processInstanceId, targetNodeId);
                    return true;
                }
            } catch (Exception apiException) {
                log.warn("使用标准API重启流程失败，尝试其他方法: {}", apiException.getMessage());
                // 这里可以实现其他的回跳逻辑
                return false;
            }

            return false;

        } catch (Exception e) {
            log.error("重新启动流程时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 跳转到指定节点
     */
    private boolean jumpToNode(String processInstanceId, String targetNodeId) {
        try {
            log.info("跳转到指定节点: 流程={}, 目标节点={}", processInstanceId, targetNodeId);

            // 获取当前活动的执行
            List<Execution> executions = runtimeService.createExecutionQuery()
                    .processInstanceId(processInstanceId)
                    .list();

            if (executions.isEmpty()) {
                log.error("没有找到活动的执行: {}", processInstanceId);
                return false;
            }

            // 获取当前活动节点
            List<String> currentActivityIds = executions.stream()
                    .map(Execution::getActivityId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (currentActivityIds.isEmpty()) {
                log.error("没有找到当前活动节点: {}", processInstanceId);
                return false;
            }

            // 使用简化的节点跳转实现
            try {
                // 这里使用简化的实现，实际项目中可能需要更复杂的逻辑
                // 可以通过设置流程变量来控制流程走向
                runtimeService.setVariable(processInstanceId, "jumpBackTarget", targetNodeId);
                runtimeService.setVariable(processInstanceId, "jumpBackFlag", true);

                log.info("节点跳转标记设置成功: 流程={}, 从节点={} 跳转到节点={}",
                        processInstanceId, currentActivityIds, targetNodeId);
                return true;

            } catch (Exception apiException) {
                log.warn("设置跳转标记失败: {}", apiException.getMessage());
                return false;
            }

        } catch (Exception e) {
            log.error("跳转节点时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理流程变量
     */
    private Map<String, Object> processVariables(String processInstanceId,
            JumpBackConfig.JumpBackDataMapping dataMapping,
            Map<String, Object> originalVariables) {
        try {
            if (dataMapping == null) {
                return originalVariables;
            }

            Map<String, Object> processedVariables = new HashMap<>(originalVariables);

            // 清除指定变量
            if (dataMapping.getClearVariables() != null) {
                for (String varName : dataMapping.getClearVariables()) {
                    processedVariables.remove(varName);
                    runtimeService.removeVariable(processInstanceId, varName);
                }
            }

            // 设置新变量
            if (dataMapping.getSetVariables() != null) {
                processedVariables.putAll(dataMapping.getSetVariables());
                runtimeService.setVariables(processInstanceId, dataMapping.getSetVariables());
            }

            // 复制变量（这里可以添加更复杂的复制逻辑）
            if (dataMapping.getCopyVariables() != null) {
                for (String varName : dataMapping.getCopyVariables()) {
                    Object value = originalVariables.get(varName);
                    if (value != null) {
                        processedVariables.put(varName + "_backup", value);
                        runtimeService.setVariable(processInstanceId, varName + "_backup", value);
                    }
                }
            }

            return processedVariables;

        } catch (Exception e) {
            log.error("处理流程变量时发生异常: {}", e.getMessage(), e);
            return originalVariables;
        }
    }

    /**
     * 获取当前流程变量
     */
    private Map<String, Object> getCurrentProcessVariables(String processInstanceId) {
        try {
            // 先尝试从运行时获取
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (processInstance != null) {
                return runtimeService.getVariables(processInstanceId);
            }

            // 如果流程已结束，从历史中获取
            return historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .list()
                    .stream()
                    .collect(Collectors.toMap(
                            var -> var.getVariableName(),
                            var -> var.getValue(),
                            (existing, replacement) -> replacement));

        } catch (Exception e) {
            log.error("获取流程变量时发生异常: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 检查目标节点是否有效
     */
    private boolean isTargetNodeValid(String processInstanceId, String targetNodeId) {
        try {
            // 获取流程定义
            HistoricProcessInstance historicInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (historicInstance == null) {
                return false;
            }

            // 检查目标节点是否在流程定义中存在
            // 这里可以通过 BpmnModel 来验证节点是否存在
            // 为简化实现，这里假设节点有效
            return targetNodeId != null && !targetNodeId.trim().isEmpty();

        } catch (Exception e) {
            log.error("检查目标节点有效性时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新回跳状态
     */
    private void updateJumpBackState(String processInstanceId, String configId, int jumpCount) {
        String stateKey = processInstanceId + "_" + configId;
        JumpBackState state = new JumpBackState();
        state.setProcessInstanceId(processInstanceId);
        state.setConfigId(configId);
        state.setJumpCount(jumpCount);
        state.setLastJumpTime(LocalDateTime.now());

        jumpBackStateCache.put(stateKey, state);
    }

    /**
     * 回跳状态内部类
     */
    private static class JumpBackState {
        private String processInstanceId;
        private String configId;
        private int jumpCount;
        private LocalDateTime lastJumpTime;

        // Getters and Setters
        public String getProcessInstanceId() {
            return processInstanceId;
        }

        public void setProcessInstanceId(String processInstanceId) {
            this.processInstanceId = processInstanceId;
        }

        public String getConfigId() {
            return configId;
        }

        public void setConfigId(String configId) {
            this.configId = configId;
        }

        public int getJumpCount() {
            return jumpCount;
        }

        public void setJumpCount(int jumpCount) {
            this.jumpCount = jumpCount;
        }

        public LocalDateTime getLastJumpTime() {
            return lastJumpTime;
        }

        public void setLastJumpTime(LocalDateTime lastJumpTime) {
            this.lastJumpTime = lastJumpTime;
        }
    }
}
