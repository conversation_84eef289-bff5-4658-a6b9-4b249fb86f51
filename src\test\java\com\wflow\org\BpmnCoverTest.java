package com.wflow.org;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson2.JSONObject;
import com.wflow.workflow.WFlowToBpmnCreator;
import com.wflow.workflow.bean.process.ProcessNode;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngineConfiguration;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.validation.ProcessValidator;
import org.flowable.validation.ProcessValidatorFactory;
import org.flowable.validation.ValidationError;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> willian fu
 * @date : 2022/9/29
 */
@SpringBootTest
public class BpmnCoverTest {

    public static void main(String[] args) throws Exception {
        // 创建Flowable引擎实例
        ProcessEngine processEngine = ProcessEngineConfiguration.createStandaloneProcessEngineConfiguration().buildProcessEngine();
        RepositoryService repositoryService = processEngine.getRepositoryService();

        // 获取流程定义
        String processDefinitionId = "wf4905c490ee3940ddb6f68bd972c97a5d"; // 替换为你的流程定义ID
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(processDefinitionId)
                .singleResult();

        // 获取BPMN模型
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());

        // 将BPMN模型转换为XML并保存
        BpmnXMLConverter bpmnXMLConverter = new BpmnXMLConverter();
        byte[] bpmnXML = bpmnXMLConverter.convertToXML(bpmnModel);

        // 保存到文件
        try (OutputStream outputStream = new FileOutputStream(new File("process-definition.bpmn"))) {
            outputStream.write(bpmnXML);
        }
    }


}
