# 表单统计功能使用说明

## 功能概述

表单统计功能用于统计运行中的表单填写的值是否匹配特定的条件，支持多种匹配模式和灵活的配置。

## 核心特性

1. **多种匹配模式**：精确匹配、包含匹配、正则表达式、数值比较、范围匹配
2. **实时统计**：支持实时查询运行中的流程数据
3. **缓存机制**：提供结果缓存以提升查询性能
4. **批量处理**：支持批量配置和批量统计
5. **详细结果**：提供匹配的具体流程实例信息

## 数据库表结构

### 统计配置表 (wflow_form_statistics_config)
```sql
CREATE TABLE wflow_form_statistics_config (
    id VARCHAR(32) PRIMARY KEY,
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    form_id VARCHAR(32) NOT NULL COMMENT '表单ID',
    field_id VARCHAR(50) NOT NULL COMMENT '字段ID',
    field_name VARCHAR(100) COMMENT '字段名称',
    field_key VARCHAR(50) COMMENT '字段键值',
    target_value TEXT NOT NULL COMMENT '目标值(JSON格式)',
    match_type VARCHAR(20) NOT NULL COMMENT '匹配类型',
    status INT DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
    create_time DATETIME,
    update_time DATETIME,
    create_by VARCHAR(32),
    update_by VARCHAR(32)
);
```

### 统计结果缓存表 (wflow_form_statistics_result)
```sql
CREATE TABLE wflow_form_statistics_result (
    id VARCHAR(32) PRIMARY KEY,
    config_id VARCHAR(32) NOT NULL,
    form_id VARCHAR(32) NOT NULL,
    field_id VARCHAR(50) NOT NULL,
    target_value VARCHAR(500) NOT NULL,
    match_count INT NOT NULL,
    total_count INT NOT NULL,
    match_rate DECIMAL(5,2) NOT NULL,
    last_update_time DATETIME NOT NULL
);
```

## 匹配类型说明

| 类型 | 代码 | 说明 | 示例 |
|------|------|------|------|
| 精确匹配 | EXACT | 完全相等 | "北京" 匹配 "北京" |
| 包含匹配 | CONTAINS | 包含指定文本 | "北京市" 包含 "北京" |
| 正则匹配 | REGEX | 正则表达式匹配 | "\\d{4}" 匹配 "2024" |
| 大于 | GREATER_THAN | 数值大于 | 100 > 50 |
| 小于 | LESS_THAN | 数值小于 | 50 < 100 |
| 不等于 | NOT_EQUALS | 不相等 | "上海" ≠ "北京" |
| 范围匹配 | RANGE | 在指定范围内 | 75 在 "60,90" 范围内 |

## API 接口说明

### 1. 创建统计配置
```http
POST /wflow/form/statistics/config
Content-Type: application/json

{
    "configName": "年龄统计",
    "formId": "form_001",
    "fieldId": "age",
    "fieldName": "年龄",
    "fieldKey": "age",
    "targetValue": "[\"25\", \"30\", \"35\"]",
    "matchType": "EXACT"
}
```

### 2. 执行实时统计
```http
GET /wflow/form/statistics/execute/{configId}?includeFinished=false
```

### 3. 批量执行统计
```http
POST /wflow/form/statistics/execute/batch?includeFinished=false
Content-Type: application/json

["config_001", "config_002", "config_003"]
```

### 4. 根据表单执行统计
```http
GET /wflow/form/statistics/execute/form/{formId}?includeFinished=false
```

## 使用示例

### 示例1：统计年龄分布
```json
{
    "configName": "员工年龄分布统计",
    "formId": "employee_form",
    "fieldId": "age",
    "fieldName": "年龄",
    "targetValue": "[\"20-30\", \"31-40\", \"41-50\", \"51-60\"]",
    "matchType": "RANGE"
}
```

### 示例2：统计部门分布
```json
{
    "configName": "部门分布统计",
    "formId": "employee_form",
    "fieldId": "department",
    "fieldName": "部门",
    "targetValue": "[\"技术部\", \"销售部\", \"人事部\", \"财务部\"]",
    "matchType": "EXACT"
}
```

### 示例3：统计薪资范围
```json
{
    "configName": "薪资范围统计",
    "formId": "salary_form",
    "fieldId": "salary",
    "fieldName": "薪资",
    "targetValue": "8000",
    "matchType": "GREATER_THAN"
}
```

### 示例4：统计地区分布（包含匹配）
```json
{
    "configName": "地区分布统计",
    "formId": "address_form",
    "fieldId": "city",
    "fieldName": "城市",
    "targetValue": "[\"北京\", \"上海\", \"广州\", \"深圳\"]",
    "matchType": "CONTAINS"
}
```

## 返回结果格式

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "configId": "config_001",
        "configName": "年龄统计",
        "formId": "form_001",
        "formName": "员工信息表",
        "fieldId": "age",
        "fieldName": "年龄",
        "fieldKey": "age",
        "targetValues": ["25", "30", "35"],
        "matchType": "EXACT",
        "details": [
            {
                "targetValue": "25",
                "matchCount": 15,
                "matchRate": 12.50,
                "matchedInstances": [
                    {
                        "instanceId": "inst_001",
                        "instanceName": "张三的申请",
                        "startUser": "user_001",
                        "startUserName": "张三",
                        "fieldValue": "25",
                        "createTime": "2024-01-01T10:00:00",
                        "status": "运行中"
                    }
                ]
            }
        ],
        "totalMatchCount": 45,
        "totalInstanceCount": 120,
        "totalMatchRate": 37.50,
        "statisticsTime": "2024-01-01T15:30:00"
    }
}
```

## 性能优化建议

1. **使用缓存**：对于频繁查询的统计，建议启用缓存机制
2. **合理设置范围**：避免统计过大范围的数据，建议按时间段分批统计
3. **索引优化**：确保表单数据表在相关字段上有适当的索引
4. **定时刷新**：对于实时性要求不高的统计，可以定时刷新缓存

## 注意事项

1. 目标值支持JSON数组格式，可以同时统计多个值
2. 范围匹配支持 "min,max" 或 "min-max" 格式
3. 正则表达式匹配需要注意转义字符
4. 数值比较会自动尝试转换为数字，失败时按字符串比较
5. 统计结果包含匹配的具体流程实例信息，便于追溯
