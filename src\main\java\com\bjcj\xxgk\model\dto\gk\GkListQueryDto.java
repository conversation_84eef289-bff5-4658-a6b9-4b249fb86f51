package com.bjcj.xxgk.model.dto.gk;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 征地信息公开列表查询参数DTO
 * 
 * <AUTHOR> Assistant
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "征地信息公开列表查询参数")
public class GkListQueryDto {

    @Schema(description = "页码", example = "1")
    private Integer pageNum;

    @Schema(description = "每页大小", example = "10")
    private Integer pageSize;

    @Schema(description = "行政区编码（对应SysXzq14表中的code字段）", example = "140000")
    private String districtCode;

    @Schema(description = "所属区（精确匹配）", example = "小店区")
    private String district;

    @Schema(description = "街道（精确匹配）", example = "坞城街道")
    private String street;

    @Schema(description = "单位（精确匹配）", example = "太原市小店区政府")
    private String unit;

    @Schema(description = "批准文号（精确匹配）", example = "晋政征[2024]001号")
    private String approvalNumber;
}
