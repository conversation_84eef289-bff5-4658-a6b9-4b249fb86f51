package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 表单字段统计配置表
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("wflow_form_statistics_config")
@Schema(description = "表单字段统计配置")
public class WflowFormStatisticsConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;

    @Schema(description = "配置名称")
    private String configName;

    @Schema(description = "表单ID")
    private String formId;

    @Schema(description = "字段ID")
    private String fieldId;

    @Schema(description = "字段名称")
    private String fieldName;

    @Schema(description = "字段设置内容")
    private String formItem ;

    @Schema(description = "目标值(支持多个值，JSON格式)")
    private String targetValue;

    @Schema(description = "匹配类型：EXACT(精确匹配)、CONTAINS(包含)、REGEX(正则)")
    private String matchType;

    @Schema(description = "状态：0=禁用，1=启用")
    private Integer status;

    @Schema(description = "统计描述")
    private String description;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新人")
    private String updateBy;
}
