package com.wflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wflow.bean.entity.*;
import com.wflow.bean.enums.MatchTypeEnum;
import com.wflow.bean.vo.FormMenuVo;
import com.wflow.bean.vo.FormStatisticsVo;
import com.wflow.bean.vo.UserVo;
import com.wflow.mapper.*;
import com.wflow.service.FormStatisticsService;
import com.wflow.service.OrgRepositoryService;
import com.wflow.utils.UserUtil;
import com.wflow.workflow.bean.vo.FormInsideVo;
import com.wflow.workflow.bean.vo.LinkVo;
import com.wflow.workflow.bean.vo.StatisticsHomeVo;
import com.wflow.workflow.utils.FlowableUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 表单统计服务实现类
 *
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Slf4j
@Service
public class FormStatisticsServiceImpl implements FormStatisticsService {

    @Autowired
    private WflowFormStatisticsConfigMapper statisticsConfigMapper;

    @Autowired
    private WflowFormStatisticsResultMapper statisticsResultMapper;

    @Autowired
    private WflowFormDataMapper formDataMapper;

    @Autowired
    private WflowModelsMapper modelsMapper;

    @Resource
    private WflowModelHistorysMapper modelHistorysMapper;

    @Autowired
    private WflowProcessInstancesMapper processInstancesMapper;

    @Autowired
    private OrgRepositoryService orgRepositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Resource
    private WflowRolesMapper rolesMapper;
    @Resource
    private  WflowIInstitutionMapper institutionMapper;
    @Resource
    private WflowUsersMapper usersMapper;
    @Override
    @Transactional
    public String createStatisticsConfig(WflowFormStatisticsConfig config) {
        config.setId(IdUtil.objectId());
        config.setCreateTime(LocalDateTime.now());
        config.setUpdateTime(LocalDateTime.now());
        config.setCreateBy(UserUtil.getLoginUserId());
        config.setStatus(1);
        statisticsConfigMapper.insert(config);
        log.info("创建统计配置成功，配置ID: {}", config.getId());
        return config.getId();
    }

    @Override
    @Transactional
    public boolean updateStatisticsConfig(WflowFormStatisticsConfig config) {
        config.setUpdateTime(LocalDateTime.now());
        config.setUpdateBy(UserUtil.getLoginUserId());

        int result = statisticsConfigMapper.updateById(config);
        if (result > 0) {
            log.info("更新统计配置成功，配置ID: {}", config.getId());
            // 清除相关缓存
            clearStatisticsCache(config.getId());
        }
        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteStatisticsConfig(String configId) {
        int result = statisticsConfigMapper.deleteById(configId);
        if (result > 0) {
            log.info("删除统计配置成功，配置ID: {}", configId);
            // 清除相关缓存
            clearStatisticsCache(configId);
        }
        return result > 0;
    }

    @Override
    @Transactional
    public boolean toggleStatisticsConfig(String configId, Integer status) {
        WflowFormStatisticsConfig config = new WflowFormStatisticsConfig();
        config.setId(configId);
        config.setStatus(status);
        config.setUpdateTime(LocalDateTime.now());
        config.setUpdateBy(UserUtil.getLoginUserId());

        int result = statisticsConfigMapper.updateById(config);
        if (result > 0) {
            log.info("切换统计配置状态成功，配置ID: {}, 状态: {}", configId, status);
            if (status == 0) {
                // 禁用时清除缓存
                clearStatisticsCache(configId);
            }
        }
        return result > 0;
    }

    @Override
    public Page<WflowFormStatisticsConfig> getStatisticsConfigs(Integer pageNo, Integer pageSize,
                                                                String formId, Integer status) {
        Page<WflowFormStatisticsConfig> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<WflowFormStatisticsConfig> wrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotBlank(formId)) {
            wrapper.eq(WflowFormStatisticsConfig::getFormId, formId);
        }
        if (status != null) {
            wrapper.eq(WflowFormStatisticsConfig::getStatus, status);
        }

        wrapper.orderByDesc(WflowFormStatisticsConfig::getCreateTime);
        return statisticsConfigMapper.selectPage(page, wrapper);
    }

    @Override
    public List<WflowFormStatisticsConfig> getStatisticsConfigsByFormId(String formId) {
        return statisticsConfigMapper.selectEnabledByFormId(formId);
    }

    @Override
    public FormStatisticsVo executeRealTimeStatistics(String configId, boolean includeFinished) {
        WflowFormStatisticsConfig config = statisticsConfigMapper.selectById(configId);
        if (config == null || config.getStatus() != 1) {
            throw new RuntimeException("统计配置不存在或已禁用");
        }

        return doStatistics(config, includeFinished, null);
    }

    @Override
    public List<FormStatisticsVo> executeBatchStatistics(List<String> configIds, boolean includeFinished) {
        List<FormStatisticsVo> results = new ArrayList<>();

        for (String configId : configIds) {
            try {
                FormStatisticsVo result = executeRealTimeStatistics(configId, includeFinished);
                results.add(result);
            } catch (Exception e) {
                log.error("执行统计失败，配置ID: {}", configId, e);
            }
        }

        return results;
    }

    @Override
    public List<FormStatisticsVo> executeStatisticsByFormId(String formId, boolean includeFinished) {
        List<WflowFormStatisticsConfig> configs = getStatisticsConfigsByFormId(formId);
        if (CollectionUtil.isEmpty(configs)) {
            return Collections.emptyList();
        }

        List<String> configIds = configs.stream()
                .map(WflowFormStatisticsConfig::getId)
                .collect(Collectors.toList());

        return executeBatchStatistics(configIds, includeFinished);
    }

    @Override
    public List<String> getFieldDistinctValues(String formId, String fieldId, Integer limit) {
        LambdaQueryWrapper<WflowFormData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WflowFormData::getCode, formId)
                .eq(WflowFormData::getFieldId, fieldId)
                .isNotNull(WflowFormData::getFieldValue)
                .ne(WflowFormData::getFieldValue, "")
                .groupBy(WflowFormData::getFieldValue)
                .orderByDesc(WflowFormData::getCreateTime);

        if (limit != null && limit > 0) {
            wrapper.last("LIMIT " + limit);
        }

        List<WflowFormData> dataList = formDataMapper.selectList(wrapper);
        return dataList.stream()
                .map(WflowFormData::getFieldValue)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean refreshStatisticsCache(String configId) {
        try {
            // 清除旧缓存
            clearStatisticsCache(configId);

            // 重新计算并缓存
            FormStatisticsVo result = executeRealTimeStatistics(configId, true);

            // 保存到缓存表
            saveStatisticsToCache(configId, result);

            log.info("刷新统计缓存成功，配置ID: {}", configId);
            return true;
        } catch (Exception e) {
            log.error("刷新统计缓存失败，配置ID: {}", configId, e);
            return false;
        }
    }
    /*
     * 查询菜单
     * */
    @Override
    public List<FormMenuVo> getMenu() {
        return statisticsConfigMapper.selectList(
                        Wrappers.<WflowFormStatisticsConfig>lambdaQuery()
                                .eq(WflowFormStatisticsConfig::getConfigName, "查询"))
                .stream()
                .map(config -> {
                            FormMenuVo vo = new FormMenuVo();
                            vo.setFieldId(config.getFieldId());
                            vo.setFieldName(config.getFieldName());
                            vo.setConfigName(config.getConfigName());
                            vo.setFormItem(JSONObject.parseObject(config.getFormItem()));
                            return vo;
                        }
                )
                .toList();
    }

    @Override
    public Object getstatistics(String type) {
        String userId = UserUtil.getLoginUserId();
        User user = usersMapper.selectById(Long.valueOf(userId));
        //查询用户角色
        List<WflowRoles> roles = rolesMapper.getRole(userId,"1729671487141314562");
        List<String> org = List.of();
        if(roles.isEmpty()){
            //查询机构和子机构
            org = orgRepositoryService.getRecursiveSubInstitution(String.valueOf(user.getInstitutionId()));
        }
        List<WflowFormStatisticsConfig> wflowFormStatisticsConfigs = statisticsConfigMapper.selectList(Wrappers.<WflowFormStatisticsConfig>lambdaQuery()
                .eq(WflowFormStatisticsConfig::getConfigName, "节点统计"));
        List<String> defId = modelHistorysMapper.selectList(Wrappers.<WflowModelHistorys>lambdaQuery()
                        .select(WflowModelHistorys::getProcessDefId)
                        .eq(WflowModelHistorys::getBusinessType,type))
                .stream()
                .map(WflowModelHistorys::getProcessDefId)
                .distinct()
                .toList();
        //查询总流程数
        Long totol = FlowableUtils.getProcessCount(defId);
        // 查询处于指定节点的未结束活动实例数量
        List<LinkVo> linkVos = wflowFormStatisticsConfigs.stream().map(map->{
            long count = historyService.createHistoricActivityInstanceQuery()
                    .activityId(map.getFieldId())
                    .unfinished()
                    .count();
            return LinkVo.builder().total(count).id(map.getFieldId()).name(map.getFieldName()).build();

        }).toList();
        //查询表单内节点统计数量
        List<FormInsideVo> formInsideVos =  doHome(org).stream().map(map-> FormInsideVo.builder()
                .id(map.getFieldId())
                .name(map.getFieldName())
                .total(String.valueOf(map.getTotalMatchCount()))
                .build()).toList();
        return StatisticsHomeVo.builder()
                .total(totol)
                .linkVos(linkVos)
                .formInsideVos(formInsideVos)
                .build();
    }

    @Override
    public Object getXzpStatistics(String xzqCode,String fieldId,String formId) {
        List<String> org = institutionMapper.selectList(
                        Wrappers.<SafetyInstitution>lambdaQuery()
                                .eq(SafetyInstitution::getXzqCode, xzqCode)
                ).stream()
                .flatMap(safetyInstitution -> {
                    // 获取父机构ID
                    String parentId = String.valueOf(safetyInstitution.getId());

                    // 获取所有递归子机构
                    List<String> subInstitutions = orgRepositoryService.getRecursiveSubInstitution(parentId);

                    // 创建包含父机构+所有子机构的流
                    return Stream.concat(
                            Stream.of(parentId),          // 父机构ID作为第一个元素
                            subInstitutions.stream()      // 所有子机构流
                    );
                }).distinct()
                .toList();       //查询机构和子机构
        return statisticsConfigMapper.selectList(Wrappers.<WflowFormStatisticsConfig>lambdaQuery()
                        .eq(StringUtils.isNotEmpty(fieldId), WflowFormStatisticsConfig::getFieldId, fieldId)
                        .eq(StringUtils.isNotEmpty(formId), WflowFormStatisticsConfig::getFormId, formId)
                ).stream()
                .findFirst() // 只取第一个匹配的配置
                .map(config -> {
                    FormStatisticsVo formStatisticsVo = doStatistics(config, true, org);
                    return FormInsideVo.builder()
                            .id(formStatisticsVo.getFieldId())
                            .name(formStatisticsVo.getFieldName())
                            .total(String.valueOf(formStatisticsVo.getTotalMatchCount()))
                            .build();
                })
                .orElse(null);
    }

    /**
     * 清除统计缓存
     */
    private void clearStatisticsCache(String configId) {
        LambdaQueryWrapper<WflowFormStatisticsResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WflowFormStatisticsResult::getConfigId, configId);
        statisticsResultMapper.delete(wrapper);
    }

    /**
     * 保存统计结果到缓存
     */
    private void saveStatisticsToCache(String configId, FormStatisticsVo result) {
        for (FormStatisticsVo.StatisticsDetail detail : result.getDetails()) {
            WflowFormStatisticsResult cacheResult = WflowFormStatisticsResult.builder()
                    .id(IdUtil.objectId())
                    .configId(configId)
                    .formId(result.getFormId())
                    .fieldId(result.getFieldId())
                    .targetValue(detail.getTargetValue())
                    .matchCount(detail.getMatchCount())
                    .totalCount(result.getTotalInstanceCount())
                    .matchRate(detail.getMatchRate())
                    .lastUpdateTime(LocalDateTime.now())
                    .build();

            statisticsResultMapper.insert(cacheResult);
        }
    }
    private List<FormStatisticsVo> doHome(List<String> org){
        List<WflowFormStatisticsConfig> wflowFormStatisticsConfigs = statisticsConfigMapper.selectList(Wrappers.<WflowFormStatisticsConfig>lambdaQuery()
                .eq(WflowFormStatisticsConfig::getConfigName, "首页统计"));
        if(!wflowFormStatisticsConfigs.isEmpty()){
            return wflowFormStatisticsConfigs.stream().map(config -> doStatistics(config,true,org)).toList();
        }
        return Collections.emptyList();
    }

    /**
     * 执行统计逻辑
     */
    private FormStatisticsVo doStatistics(WflowFormStatisticsConfig config, boolean includeFinished,List<String> org) {
        // 解析目标值
        List<String> targetValues = parseTargetValues(config.getTargetValue());

        // 获取表单信息
        WflowModels model = modelsMapper.selectById(config.getFormId());
        String formName = model != null ? model.getFormName() : "未知表单";

        // 查询表单数据
        List<WflowFormData> formDataList = getFormDataByConfig(config, includeFinished, org);

        // 获取所有相关的流程实例
        Set<String> instanceIds = formDataList.stream()
                .map(WflowFormData::getInstanceId)
                .collect(Collectors.toSet());


        Map<String, HistoricProcessInstance> instanceMap = getProcessInstanceMap(instanceIds);
        Map<String, String> userNameMap = getUserNameMap(instanceMap.values());

        // 执行匹配统计
        List<FormStatisticsVo.StatisticsDetail> details = new ArrayList<>();
        BigDecimal totalMatchCount = BigDecimal.ZERO;
        if("SUM".equals(config.getMatchType())){
            totalMatchCount =  calculateFieldSum(config.getFormId(),config.getFieldId(),true);
        }else {
            for (String targetValue : targetValues) {
                List<WflowFormData> matchedData = filterMatchedData(formDataList, targetValue, config.getMatchType());
                List<FormStatisticsVo.MatchedInstance> matchedInstances = buildMatchedInstances(
                        matchedData, instanceMap, userNameMap);

                BigDecimal matchCount = BigDecimal.valueOf(matchedData.size());
                totalMatchCount = totalMatchCount.add(matchCount);

//                BigDecimal matchRate = calculateMatchRate(matchCount, formDataList.size());
//
//                FormStatisticsVo.StatisticsDetail detail = FormStatisticsVo.StatisticsDetail.builder()
//                        .targetValue(targetValue)
//                        .matchCount(matchCount)
//                        .matchRate(matchRate)
//                        .matchedInstances(matchedInstances)
//                        .build();
//
//                details.add(detail);
            }
        }

//        BigDecimal totalMatchRate = calculateMatchRate(totalMatchCount, formDataList.size());

        return FormStatisticsVo.builder()
                .configId(config.getId())
                .configName(config.getConfigName())
                .formId(config.getFormId())
                .formName(formName)
                .fieldId(config.getFieldId())
                .fieldName(config.getFieldName())
                .targetValues(targetValues)
                .matchType(config.getMatchType())
                .totalMatchCount(totalMatchCount)
                .totalInstanceCount(formDataList.size())
                .statisticsTime(LocalDateTime.now())
                .build();
    }

    /**
     * 解析目标值
     */
    private List<String> parseTargetValues(String targetValueJson) {
        try {
            if (targetValueJson.startsWith("[")) {
                // JSON数组格式
                JSONArray jsonArray = JSON.parseArray(targetValueJson);
                return jsonArray.toJavaList(String.class);
            } else {
                // 单个值
                return Collections.singletonList(targetValueJson);
            }
        } catch (Exception e) {
            log.warn("解析目标值失败，使用原始值: {}", targetValueJson, e);
            return Collections.singletonList(targetValueJson);
        }
    }

    /**
     * 根据配置查询表单数据
     */
    private List<WflowFormData> getFormDataByConfig(WflowFormStatisticsConfig config, boolean includeFinished,List<String> org) {
        LambdaQueryWrapper<WflowFormData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WflowFormData::getCode, config.getFormId())
                .eq(WflowFormData::getFieldId, config.getFieldId())
                .isNotNull(WflowFormData::getFieldValue)
                .ne(WflowFormData::getFieldValue, "");

        // 原始数据查询
        List<WflowFormData> allData = formDataMapper.selectList(wrapper);
        if(org == null || org.isEmpty()){
            return allData;
        }

// 获取流程实例ID集合
        Set<String> instanceIds = allData.stream()
                .map(WflowFormData::getInstanceId)
                .collect(Collectors.toSet());

// 查询历史流程实例
        List<HistoricProcessInstance> instances = historyService.createHistoricProcessInstanceQuery()
                .processInstanceIds(instanceIds)
                .list();

// 构建用户ID->机构ID的映射 (避免后续嵌套循环)
        Map<Long, Long> userIdToOrgIdMap = instances.stream()
                .map(HistoricProcessInstance::getStartUserId)
                .filter(Objects::nonNull)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toMap(
                        userId -> userId,
                        userId -> usersMapper.selectById(userId).getInstitutionId(),
                        (existing, replacement) -> existing
                ));

// 构建流程实例ID->机构ID的映射
        Map<String, Long> instanceOrgMap = instances.stream()
                .filter(inst -> userIdToOrgIdMap.containsKey(Long.valueOf(inst.getStartUserId())))
                .collect(Collectors.toMap(
                        HistoricProcessInstance::getId,
                        inst -> userIdToOrgIdMap.get(Long.valueOf(inst.getStartUserId()))
                ));

// 假设这是需要过滤的机构ID白名单
        Set<Long> allowedOrgIds = org.stream().map(Long::valueOf).collect(Collectors.toSet());

// 过滤原始数据：只保留机构ID在白名单中的数据
        List<WflowFormData> filteredData = allData.stream()
                .filter(data -> {
                    Long orgId = instanceOrgMap.get(data.getInstanceId());
                    return orgId != null && allowedOrgIds.contains(orgId);
                })
                .toList();

        if (includeFinished) {
            return filteredData;
        }

        // 只包含运行中的流程
        Set<String> runningInstanceIds = getRunningInstanceIds();
        return filteredData.stream()
                .filter(data -> runningInstanceIds.contains(data.getInstanceId()))
                .collect(Collectors.toList());
    }

    /**
     * 获取运行中的流程实例ID
     */
    private Set<String> getRunningInstanceIds() {
        List<ProcessInstance> runningInstances = runtimeService.createProcessInstanceQuery().list();
        return runningInstances.stream()
                .map(ProcessInstance::getId)
                .collect(Collectors.toSet());
    }

    /**
     * 获取流程实例映射
     */
    private Map<String, HistoricProcessInstance> getProcessInstanceMap(Set<String> instanceIds) {
        if (CollectionUtil.isEmpty(instanceIds)) {
            return Collections.emptyMap();
        }

        List<HistoricProcessInstance> instances = historyService.createHistoricProcessInstanceQuery()
                .processInstanceIds(instanceIds)
                .list();

        return instances.stream()
                .collect(Collectors.toMap(HistoricProcessInstance::getId, instance -> instance));
    }

    /**
     * 获取用户名映射
     */
    private Map<String, String> getUserNameMap(Collection<HistoricProcessInstance> instances) {
        Set<String> userIds = instances.stream()
                .map(HistoricProcessInstance::getStartUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }

        Map<String, String> userNameMap = new HashMap<>();
        for (String userId : userIds) {
            try {
                String userName = orgRepositoryService.getUserById(userId).getUserName();
                userNameMap.put(userId, userName);
            } catch (Exception e) {
                log.warn("获取用户名失败，用户ID: {}", userId, e);
                userNameMap.put(userId, "未知用户");
            }
        }

        return userNameMap;
    }

    /**
     * 过滤匹配的数据
     */
    private List<WflowFormData> filterMatchedData(List<WflowFormData> dataList, String targetValue, String matchType) {
        MatchTypeEnum matchTypeEnum = MatchTypeEnum.getByCode(matchType);

        return dataList.stream()
                .filter(data -> isValueMatched(data.getFieldValue(), targetValue, matchTypeEnum))
                .collect(Collectors.toList());
    }

    /**
     * 判断值是否匹配
     */
    private boolean isValueMatched(String fieldValue, String targetValue, MatchTypeEnum matchType) {
        if (fieldValue == null || targetValue == null) {
            return false;
        }

        switch (matchType) {
            case EXACT:
            case EQUALS:
                return fieldValue.equals(targetValue);
            case CONTAINS:
                return fieldValue.contains(targetValue);
            case REGEX:
                try {
                    return Pattern.matches(targetValue, fieldValue);
                } catch (Exception e) {
                    log.warn("正则表达式匹配失败: {}", targetValue, e);
                    return false;
                }
            case GREATER_THAN:
                return compareNumeric(fieldValue, targetValue) > 0;
            case LESS_THAN:
                return compareNumeric(fieldValue, targetValue) < 0;
            case NOT_EQUALS:
                return !fieldValue.equals(targetValue);
            case RANGE:
                return isInRange(fieldValue, targetValue);
            default:
                return fieldValue.equals(targetValue);
        }
    }

    /**
     * 数值比较
     */
    private int compareNumeric(String value1, String value2) {
        try {
            BigDecimal num1 = new BigDecimal(value1);
            BigDecimal num2 = new BigDecimal(value2);
            return num1.compareTo(num2);
        } catch (NumberFormatException e) {
            // 如果不是数值，则按字符串比较
            return value1.compareTo(value2);
        }
    }

    /**
     * 判断是否在范围内
     */
    private boolean isInRange(String value, String rangeValue) {
        try {
            // 范围格式: "min,max" 或 "min-max"
            String[] parts = rangeValue.contains(",") ? rangeValue.split(",") : rangeValue.split("-");
            if (parts.length != 2) {
                return false;
            }

            BigDecimal num = new BigDecimal(value);
            BigDecimal min = new BigDecimal(parts[0].trim());
            BigDecimal max = new BigDecimal(parts[1].trim());

            return num.compareTo(min) >= 0 && num.compareTo(max) <= 0;
        } catch (Exception e) {
            log.warn("范围匹配失败: value={}, range={}", value, rangeValue, e);
            return false;
        }
    }

    /**
     * 构建匹配的实例列表
     */
    private List<FormStatisticsVo.MatchedInstance> buildMatchedInstances(
            List<WflowFormData> matchedData,
            Map<String, HistoricProcessInstance> instanceMap,
            Map<String, String> userNameMap) {

        return matchedData.stream().<FormStatisticsVo.MatchedInstance>map(data -> {
                    HistoricProcessInstance instance = instanceMap.get(data.getInstanceId());
                    if (instance == null) {
                        return null;
                    }

                    String startUserId = instance.getStartUserId();
                    String startUserName = userNameMap.getOrDefault(startUserId, "未知用户");

                    LocalDateTime createTime = null;
                    if (data.getCreateTime() != null) {
                        createTime = data.getCreateTime().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime();
                    }

                    return FormStatisticsVo.MatchedInstance.builder()
                            .instanceId(data.getInstanceId())
                            .instanceName(instance.getName())
                            .startUser(startUserId)
                            .startUserName(startUserName)
                            .fieldValue(data.getFieldValue())
                            .createTime(createTime)
                            .status(instance.getEndTime() != null ? "已完成" : "运行中")
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 计算匹配率
     */
    private BigDecimal calculateMatchRate(int matchCount, int totalCount) {
        if (totalCount == 0) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(matchCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(totalCount), 2, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal calculateFieldSum(String formId, String fieldId, boolean includeFinished) {
        return calculateFieldSumWithOrgFilter(formId, fieldId, includeFinished, null);
    }

    @Override
    public BigDecimal calculateFieldSumWithOrgFilter(String formId, String fieldId, boolean includeFinished,
            List<String> orgIds) {
        if (StrUtil.isBlank(formId) || StrUtil.isBlank(fieldId)) {
            log.warn("表单ID或字段ID不能为空");
            return BigDecimal.ZERO;
        }

        try {
            // 查询表单数据
            List<WflowFormData> formDataList = getFormDataForSum(formId, fieldId, includeFinished, orgIds);

            if (CollectionUtil.isEmpty(formDataList)) {
                log.info("未找到表单数据，表单ID: {}, 字段ID: {}", formId, fieldId);
                return BigDecimal.ZERO;
            }

            // 计算数值总和
            BigDecimal sum = BigDecimal.ZERO;
            int validCount = 0;
            int invalidCount = 0;

            for (WflowFormData data : formDataList) {
                String fieldValue = data.getFieldValue();
                if (StrUtil.isBlank(fieldValue)) {
                    continue;
                }

                try {
                    // 尝试将字段值转换为数值
                    BigDecimal value = new BigDecimal(fieldValue.trim());
                    sum = sum.add(value);
                    validCount++;
                } catch (NumberFormatException e) {
                    invalidCount++;
                    log.debug("字段值无法转换为数值，实例ID: {}, 字段值: {}", data.getInstanceId(), fieldValue);
                }
            }

            log.info("字段求和完成，表单ID: {}, 字段ID: {}, 总和: {}, 有效数据: {}, 无效数据: {}",
                    formId, fieldId, sum, validCount, invalidCount);

            return sum;
        } catch (Exception e) {
            log.error("计算字段求和失败，表单ID: {}, 字段ID: {}", formId, fieldId, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public List<WflowFormStatisticsConfig> getStatisticsConfigsByType(String type) {
        return statisticsConfigMapper.selectList(new LambdaQueryWrapper<WflowFormStatisticsConfig>()
                .eq(WflowFormStatisticsConfig::getConfigName, type)
                .eq(WflowFormStatisticsConfig::getStatus, 1));
    }

    /**
     * 查询用于求和计算的表单数据
     */
    private List<WflowFormData> getFormDataForSum(String formId, String fieldId, boolean includeFinished,
            List<String> orgIds) {
        LambdaQueryWrapper<WflowFormData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WflowFormData::getCode, formId)
                .eq(WflowFormData::getFieldId, fieldId)
                .isNotNull(WflowFormData::getFieldValue)
                .ne(WflowFormData::getFieldValue, "");

        // 查询所有原始数据
        List<WflowFormData> allData = formDataMapper.selectList(wrapper);

        if (CollectionUtil.isEmpty(allData)) {
            return Collections.emptyList();
        }

        // 如果没有指定机构过滤，则根据当前用户权限自动获取机构范围
        List<String> finalOrgIds = orgIds;
        if (finalOrgIds == null) {
            finalOrgIds = getCurrentUserOrgIds();
        }

        // 如果机构ID列表为空，返回所有数据（超级管理员权限）
        if (CollectionUtil.isEmpty(finalOrgIds)) {
            return filterByProcessStatus(allData, includeFinished);
        }

        // 按机构权限过滤数据
        List<WflowFormData> filteredData = filterDataByOrg(allData, finalOrgIds);

        // 按流程状态过滤
        return filterByProcessStatus(filteredData, includeFinished);
    }

    /**
     * 获取当前用户的机构权限范围
     */
    private List<String> getCurrentUserOrgIds() {
        try {
            String userId = UserUtil.getLoginUserId();
            User user = usersMapper.selectById(Long.valueOf(userId));

            // 查询用户角色，判断是否为超级管理员
            List<WflowRoles> roles = rolesMapper.getRole(userId, "1729671487141314562");

            if (CollectionUtil.isEmpty(roles)) {
                // 普通用户，查询机构和子机构
                return orgRepositoryService.getRecursiveSubInstitution(String.valueOf(user.getInstitutionId()));
            } else {
                // 超级管理员，返回空列表表示不限制机构
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("获取当前用户机构权限失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 按机构权限过滤数据
     */
    private List<WflowFormData> filterDataByOrg(List<WflowFormData> allData, List<String> orgIds) {
        // 获取流程实例ID集合
        Set<String> instanceIds = allData.stream()
                .map(WflowFormData::getInstanceId)
                .collect(Collectors.toSet());

        if (CollectionUtil.isEmpty(instanceIds)) {
            return Collections.emptyList();
        }

        // 获取流程实例信息
        List<HistoricProcessInstance> instances = historyService.createHistoricProcessInstanceQuery()
                .processInstanceIds(instanceIds)
                .list();

        // 获取用户ID到机构ID的映射
        Set<String> userIds = instances.stream()
                .map(HistoricProcessInstance::getStartUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<Long, Long> userIdToOrgIdMap = new HashMap<>();
        for (String userId : userIds) {
            try {
                User user = usersMapper.selectById(Long.valueOf(userId));
                if (user != null && user.getInstitutionId() != null) {
                    userIdToOrgIdMap.put(Long.valueOf(userId), user.getInstitutionId());
                }
            } catch (Exception e) {
                log.warn("获取用户机构信息失败，用户ID: {}", userId, e);
            }
        }

        // 构建流程实例ID->机构ID的映射
        Map<String, Long> instanceOrgMap = instances.stream()
                .filter(inst -> userIdToOrgIdMap.containsKey(Long.valueOf(inst.getStartUserId())))
                .collect(Collectors.toMap(
                        HistoricProcessInstance::getId,
                        inst -> userIdToOrgIdMap.get(Long.valueOf(inst.getStartUserId()))));

        // 机构ID白名单
        Set<Long> allowedOrgIds = orgIds.stream().map(Long::valueOf).collect(Collectors.toSet());

        // 过滤数据：只保留机构ID在白名单中的数据
        return allData.stream()
                .filter(data -> {
                    Long orgId = instanceOrgMap.get(data.getInstanceId());
                    return orgId != null && allowedOrgIds.contains(orgId);
                })
                .collect(Collectors.toList());
    }

    /**
     * 按流程状态过滤数据
     */
    private List<WflowFormData> filterByProcessStatus(List<WflowFormData> data, boolean includeFinished) {
        if (includeFinished) {
            return data;
        }

        // 只包含运行中的流程
        Set<String> runningInstanceIds = getRunningInstanceIds();
        return data.stream()
                .filter(formData -> runningInstanceIds.contains(formData.getInstanceId()))
                .collect(Collectors.toList());
    }
}
