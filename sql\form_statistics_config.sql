-- 表单字段统计配置表
CREATE TABLE IF NOT EXISTS wflow_form_statistics_config (
    id VARCHAR(40) NOT NULL,
    config_name VARCHAR(255) NOT NULL,
    form_id VARCHAR(40) NOT NULL,
    field_id VARCHAR(40) NOT NULL,
    field_key VARCHAR(40) NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    target_value TEXT NOT NULL,
    match_type VARCHAR(20) NOT NULL DEFAULT 'EXACT',
    status SMALLINT NOT NULL DEFAULT 1,
    description TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by VA<PERSON>HAR(40),
    update_by VA<PERSON><PERSON><PERSON>(40),
    PRIMARY KEY (id)
);

-- 添加表和字段注释
COMMENT ON TABLE wflow_form_statistics_config IS '表单字段统计配置表';
COMMENT ON COLUMN wflow_form_statistics_config.id IS '主键';
COMMENT ON COLUMN wflow_form_statistics_config.config_name IS '统计配置名称';
COMMENT ON COLUMN wflow_form_statistics_config.form_id IS '表单ID';
COMMENT ON COLUMN wflow_form_statistics_config.field_id IS '字段ID';
COMMENT ON COLUMN wflow_form_statistics_config.field_key IS '字段key';
COMMENT ON COLUMN wflow_form_statistics_config.field_name IS '字段名称';
COMMENT ON COLUMN wflow_form_statistics_config.target_value IS '目标值(支持多个值，JSON格式)';
COMMENT ON COLUMN wflow_form_statistics_config.match_type IS '匹配类型：EXACT(精确匹配)、CONTAINS(包含)、REGEX(正则)、GREATER_THAN(大于)、LESS_THAN(小于)、NOT_EQUALS(不等于)、RANGE(范围)';
COMMENT ON COLUMN wflow_form_statistics_config.status IS '状态：0=禁用，1=启用';
COMMENT ON COLUMN wflow_form_statistics_config.description IS '统计描述';
COMMENT ON COLUMN wflow_form_statistics_config.create_time IS '创建时间';
COMMENT ON COLUMN wflow_form_statistics_config.update_time IS '更新时间';
COMMENT ON COLUMN wflow_form_statistics_config.create_by IS '创建人';
COMMENT ON COLUMN wflow_form_statistics_config.update_by IS '更新人';

-- 创建索引
CREATE INDEX idx_form_statistics_config_form_field ON wflow_form_statistics_config(form_id, field_id);
CREATE INDEX idx_form_statistics_config_status ON wflow_form_statistics_config(status);

-- 统计结果缓存表（可选，用于提高查询性能）
CREATE TABLE IF NOT EXISTS wflow_form_statistics_result (
    id VARCHAR(40) NOT NULL,
    config_id VARCHAR(40) NOT NULL,
    form_id VARCHAR(40) NOT NULL,
    field_id VARCHAR(40) NOT NULL,
    target_value VARCHAR(500) NOT NULL,
    match_count INTEGER NOT NULL DEFAULT 0,
    total_count INTEGER NOT NULL DEFAULT 0,
    match_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    last_update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    CONSTRAINT uk_config_value UNIQUE (config_id, target_value)
);

-- 添加表和字段注释
COMMENT ON TABLE wflow_form_statistics_result IS '表单字段统计结果缓存表';
COMMENT ON COLUMN wflow_form_statistics_result.id IS '主键';
COMMENT ON COLUMN wflow_form_statistics_result.config_id IS '统计配置ID';
COMMENT ON COLUMN wflow_form_statistics_result.form_id IS '表单ID';
COMMENT ON COLUMN wflow_form_statistics_result.field_id IS '字段ID';
COMMENT ON COLUMN wflow_form_statistics_result.target_value IS '目标值';
COMMENT ON COLUMN wflow_form_statistics_result.match_count IS '匹配数量';
COMMENT ON COLUMN wflow_form_statistics_result.total_count IS '总数量';
COMMENT ON COLUMN wflow_form_statistics_result.match_rate IS '匹配率(%)';
COMMENT ON COLUMN wflow_form_statistics_result.last_update_time IS '最后更新时间';

-- 创建索引
CREATE INDEX idx_form_statistics_result_config_id ON wflow_form_statistics_result(config_id);
CREATE INDEX idx_form_statistics_result_form_field ON wflow_form_statistics_result(form_id, field_id);
CREATE INDEX idx_form_statistics_result_update_time ON wflow_form_statistics_result(last_update_time);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为配置表创建更新时间触发器
CREATE TRIGGER update_wflow_form_statistics_config_updated_time
    BEFORE UPDATE ON wflow_form_statistics_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- 为结果表创建更新时间触发器
CREATE TRIGGER update_wflow_form_statistics_result_updated_time
    BEFORE UPDATE ON wflow_form_statistics_result
    FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
