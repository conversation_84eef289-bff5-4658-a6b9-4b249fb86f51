package com.wflow.workflow.service.impl;

import com.wflow.workflow.bean.jumpback.JumpBackConfig;
import com.wflow.workflow.service.JumpBackConfigService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.ExtensionElement;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.Gateway;
import org.flowable.engine.RepositoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 流程回跳配置服务实现
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/23
 */
@Slf4j
@Service
public class JumpBackConfigServiceImpl implements JumpBackConfigService {

    @Autowired
    private RepositoryService repositoryService;

    // 配置缓存，实际项目中应该使用数据库存储
    private final Map<String, JumpBackConfig> configCache = new ConcurrentHashMap<>();
    private final Map<String, List<JumpBackConfig>> processConfigCache = new ConcurrentHashMap<>();

    @Override
    public List<JumpBackConfig> parseJumpBackConfigs(String processDefinitionId) {
        log.info("解析流程定义中的回跳配置: {}", processDefinitionId);
        
        try {
            // 获取 BPMN 模型
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
            if (bpmnModel == null) {
                log.warn("无法获取流程定义的 BPMN 模型: {}", processDefinitionId);
                return new ArrayList<>();
            }

            List<JumpBackConfig> configs = new ArrayList<>();
            
            // 遍历所有流程元素
            Collection<FlowElement> flowElements = bpmnModel.getMainProcess().getFlowElements();
            for (FlowElement flowElement : flowElements) {
                // 检查是否为网关节点
                if (flowElement instanceof Gateway) {
                    Gateway gateway = (Gateway) flowElement;
                    // 解析扩展属性中的回跳配置
                    JumpBackConfig config = parseJumpBackConfigFromElement(gateway, processDefinitionId);
                    if (config != null) {
                        configs.add(config);
                        log.debug("解析到回跳配置: 节点={}, 目标={}", config.getSourceNodeId(), config.getTargetNodeId());
                    }
                }
            }

            // 缓存配置
            processConfigCache.put(processDefinitionId, configs);
            
            log.info("解析完成，共找到 {} 个回跳配置", configs.size());
            return configs;

        } catch (Exception e) {
            log.error("解析回跳配置时发生异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public String saveJumpBackConfig(JumpBackConfig config) {
        try {
            if (config.getId() == null) {
                config.setId(UUID.randomUUID().toString());
            }
            
            config.setCreateTime(LocalDateTime.now());
            config.setUpdateTime(LocalDateTime.now());
            
            // 保存到缓存（实际项目中应该保存到数据库）
            configCache.put(config.getId(), config);
            
            // 更新流程配置缓存
            String processDefId = config.getProcessDefId();
            List<JumpBackConfig> processConfigs = processConfigCache.computeIfAbsent(processDefId, k -> new ArrayList<>());
            processConfigs.removeIf(c -> c.getId().equals(config.getId()));
            processConfigs.add(config);
            
            log.info("保存回跳配置成功: {}", config.getId());
            return config.getId();
            
        } catch (Exception e) {
            log.error("保存回跳配置时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public JumpBackConfig getJumpBackConfig(String processDefId, String nodeId) {
        try {
            List<JumpBackConfig> configs = processConfigCache.get(processDefId);
            if (configs == null) {
                // 尝试从流程定义中解析
                configs = parseJumpBackConfigs(processDefId);
            }
            
            return configs.stream()
                    .filter(config -> nodeId.equals(config.getSourceNodeId()))
                    .findFirst()
                    .orElse(null);
                    
        } catch (Exception e) {
            log.error("获取回跳配置时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<JumpBackConfig> getJumpBackConfigsByProcessDef(String processDefId) {
        try {
            List<JumpBackConfig> configs = processConfigCache.get(processDefId);
            if (configs == null) {
                // 尝试从流程定义中解析
                configs = parseJumpBackConfigs(processDefId);
            }
            
            return new ArrayList<>(configs);
            
        } catch (Exception e) {
            log.error("获取流程回跳配置时发生异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean updateJumpBackConfig(JumpBackConfig config) {
        try {
            if (config.getId() == null || !configCache.containsKey(config.getId())) {
                log.warn("回跳配置不存在: {}", config.getId());
                return false;
            }
            
            config.setUpdateTime(LocalDateTime.now());
            configCache.put(config.getId(), config);
            
            // 更新流程配置缓存
            String processDefId = config.getProcessDefId();
            List<JumpBackConfig> processConfigs = processConfigCache.get(processDefId);
            if (processConfigs != null) {
                processConfigs.removeIf(c -> c.getId().equals(config.getId()));
                processConfigs.add(config);
            }
            
            log.info("更新回跳配置成功: {}", config.getId());
            return true;
            
        } catch (Exception e) {
            log.error("更新回跳配置时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean deleteJumpBackConfig(String configId) {
        try {
            JumpBackConfig config = configCache.remove(configId);
            if (config == null) {
                log.warn("回跳配置不存在: {}", configId);
                return false;
            }
            
            // 从流程配置缓存中移除
            String processDefId = config.getProcessDefId();
            List<JumpBackConfig> processConfigs = processConfigCache.get(processDefId);
            if (processConfigs != null) {
                processConfigs.removeIf(c -> c.getId().equals(configId));
            }
            
            log.info("删除回跳配置成功: {}", configId);
            return true;
            
        } catch (Exception e) {
            log.error("删除回跳配置时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean toggleJumpBackConfig(String configId, boolean enabled) {
        try {
            JumpBackConfig config = configCache.get(configId);
            if (config == null) {
                log.warn("回跳配置不存在: {}", configId);
                return false;
            }
            
            config.setEnabled(enabled);
            config.setUpdateTime(LocalDateTime.now());
            
            log.info("切换回跳配置状态成功: {} -> {}", configId, enabled);
            return true;
            
        } catch (Exception e) {
            log.error("切换回跳配置状态时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public ValidationResult validateJumpBackConfig(JumpBackConfig config) {
        try {
            // 基本字段验证
            if (config.getProcessDefId() == null || config.getProcessDefId().trim().isEmpty()) {
                return ValidationResult.failure("流程定义ID不能为空");
            }
            
            if (config.getSourceNodeId() == null || config.getSourceNodeId().trim().isEmpty()) {
                return ValidationResult.failure("源节点ID不能为空");
            }
            
            if (config.getTargetNodeId() == null || config.getTargetNodeId().trim().isEmpty()) {
                return ValidationResult.failure("目标节点ID不能为空");
            }
            
            if (config.getSourceNodeId().equals(config.getTargetNodeId())) {
                return ValidationResult.failure("源节点和目标节点不能相同");
            }
            
            if (config.getMaxJumpCount() == null || config.getMaxJumpCount() <= 0) {
                return ValidationResult.failure("最大回跳次数必须大于0");
            }
            
            // 验证节点是否存在于流程定义中
            if (!validateNodeExistsInProcess(config.getProcessDefId(), config.getSourceNodeId())) {
                return ValidationResult.failure("源节点在流程定义中不存在: " + config.getSourceNodeId());
            }
            
            if (!validateNodeExistsInProcess(config.getProcessDefId(), config.getTargetNodeId())) {
                return ValidationResult.failure("目标节点在流程定义中不存在: " + config.getTargetNodeId());
            }
            
            return ValidationResult.success();
            
        } catch (Exception e) {
            log.error("验证回跳配置时发生异常: {}", e.getMessage(), e);
            return ValidationResult.failure("验证配置时发生异常: " + e.getMessage());
        }
    }

    /**
     * 从流程元素中解析回跳配置
     */
    private JumpBackConfig parseJumpBackConfigFromElement(Gateway gateway, String processDefinitionId) {
        try {
            Map<String, List<ExtensionElement>> extensionElements = gateway.getExtensionElements();
            if (extensionElements == null || !extensionElements.containsKey("jumpBackConfig")) {
                return null;
            }
            
            List<ExtensionElement> jumpBackElements = extensionElements.get("jumpBackConfig");
            if (jumpBackElements.isEmpty()) {
                return null;
            }
            
            ExtensionElement jumpBackElement = jumpBackElements.get(0);
            
            // 解析配置属性
            JumpBackConfig config = new JumpBackConfig();
            config.setId(UUID.randomUUID().toString());
            config.setProcessDefId(processDefinitionId);
            config.setSourceNodeId(gateway.getId());
            config.setNodeType(JumpBackConfig.NodeType.fromValue(gateway.getClass().getSimpleName()));
            
            // 解析扩展属性
            Map<String, List<ExtensionElement>> childElements = jumpBackElement.getChildElements();
            
            if (childElements.containsKey("enabled")) {
                String enabled = getElementText(childElements.get("enabled"));
                config.setEnabled(Boolean.parseBoolean(enabled));
            } else {
                config.setEnabled(true);
            }
            
            if (childElements.containsKey("targetNodeId")) {
                String targetNodeId = getElementText(childElements.get("targetNodeId"));
                config.setTargetNodeId(targetNodeId);
            }
            
            if (childElements.containsKey("condition")) {
                String condition = getElementText(childElements.get("condition"));
                config.setJumpCondition(JumpBackConfig.JumpBackCondition.fromValue(condition));
            } else {
                config.setJumpCondition(JumpBackConfig.JumpBackCondition.ALL_BRANCHES_COMPLETED);
            }
            
            if (childElements.containsKey("maxJumpCount")) {
                String maxJumpCount = getElementText(childElements.get("maxJumpCount"));
                config.setMaxJumpCount(Integer.parseInt(maxJumpCount));
            } else {
                config.setMaxJumpCount(1);
            }
            
            // 解析数据映射配置
            if (childElements.containsKey("dataMapping")) {
                JumpBackConfig.JumpBackDataMapping dataMapping = parseDataMapping(childElements.get("dataMapping"));
                config.setDataMapping(dataMapping);
            }
            
            config.setCreateTime(LocalDateTime.now());
            config.setUpdateTime(LocalDateTime.now());
            
            return config;
            
        } catch (Exception e) {
            log.error("解析流程元素回跳配置时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析数据映射配置
     */
    private JumpBackConfig.JumpBackDataMapping parseDataMapping(List<ExtensionElement> dataMappingElements) {
        // 这里可以实现更复杂的数据映射解析逻辑
        // 为简化实现，返回默认配置
        return JumpBackConfig.JumpBackDataMapping.builder()
                .keepHistoryVariables(true)
                .build();
    }

    /**
     * 获取扩展元素的文本内容
     */
    private String getElementText(List<ExtensionElement> elements) {
        if (elements == null || elements.isEmpty()) {
            return null;
        }
        return elements.get(0).getElementText();
    }

    /**
     * 验证节点是否存在于流程定义中
     */
    private boolean validateNodeExistsInProcess(String processDefinitionId, String nodeId) {
        try {
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
            if (bpmnModel == null) {
                return false;
            }
            
            FlowElement flowElement = bpmnModel.getMainProcess().getFlowElement(nodeId);
            return flowElement != null;
            
        } catch (Exception e) {
            log.error("验证节点存在性时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
}
