package com.wflow.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 表单字段统计结果缓存表
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("wflow_form_statistics_result")
@Schema(description = "表单字段统计结果缓存")
public class WflowFormStatisticsResult implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;

    @Schema(description = "统计配置ID")
    private String configId;

    @Schema(description = "表单ID")
    private String formId;

    @Schema(description = "字段ID")
    private String fieldId;

    @Schema(description = "目标值")
    private String targetValue;

    @Schema(description = "匹配数量")
    private Integer matchCount;

    @Schema(description = "总数量")
    private Integer totalCount;

    @Schema(description = "匹配率(%)")
    private BigDecimal matchRate;

    @Schema(description = "最后更新时间")
    private LocalDateTime lastUpdateTime;
}
