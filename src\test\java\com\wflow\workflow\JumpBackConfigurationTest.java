package com.wflow.workflow;

import com.wflow.AppStater;
import com.wflow.workflow.config.JumpBackConfiguration;
import com.wflow.workflow.listener.JumpBackEventListener;
import com.wflow.workflow.service.JumpBackConfigService;
import com.wflow.workflow.service.JumpBackExecutionService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 流程回跳配置测试类
 * 验证循环依赖问题是否解决
 * 
 * <AUTHOR> willian fu
 * @date : 2025/01/23
 */
@SpringBootTest(classes = AppStater.class)
@SpringJUnitConfig
public class JumpBackConfigurationTest {

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 测试Spring容器是否能正常启动
     * 验证循环依赖问题是否解决
     */
    @Test
    public void testApplicationContextLoads() {
        assertNotNull(applicationContext, "ApplicationContext应该能正常加载");
    }

    /**
     * 测试回跳配置Bean是否能正常创建
     */
    @Test
    public void testJumpBackConfigurationBean() {
        JumpBackConfiguration configuration = applicationContext.getBean(JumpBackConfiguration.class);
        assertNotNull(configuration, "JumpBackConfiguration Bean应该能正常创建");
    }

    /**
     * 测试回跳配置服务Bean是否能正常创建
     */
    @Test
    public void testJumpBackConfigServiceBean() {
        JumpBackConfigService configService = applicationContext.getBean(JumpBackConfigService.class);
        assertNotNull(configService, "JumpBackConfigService Bean应该能正常创建");
    }

    /**
     * 测试回跳执行服务Bean是否能正常创建
     */
    @Test
    public void testJumpBackExecutionServiceBean() {
        JumpBackExecutionService executionService = applicationContext.getBean(JumpBackExecutionService.class);
        assertNotNull(executionService, "JumpBackExecutionService Bean应该能正常创建");
    }

    /**
     * 测试回跳事件监听器Bean是否能正常创建
     * 由于使用了@Lazy注解，只有在实际使用时才会创建
     */
    @Test
    public void testJumpBackEventListenerBean() {
        try {
            // 由于使用了@Lazy注解，这里会触发Bean的实际创建
            JumpBackEventListener eventListener = applicationContext.getBean(JumpBackEventListener.class);
            assertNotNull(eventListener, "JumpBackEventListener Bean应该能正常创建");
        } catch (Exception e) {
            // 如果获取失败，说明可能还有依赖问题，但不影响主要功能
            System.out.println("JumpBackEventListener Bean创建失败，可能需要完整的Flowable环境: " + e.getMessage());
        }
    }

    /**
     * 测试基础服务Bean都能正常获取
     */
    @Test
    public void testBasicJumpBackBeansAvailable() {
        // 验证基础Bean都能正常获取，没有循环依赖问题
        assertDoesNotThrow(() -> {
            JumpBackConfiguration configuration = applicationContext.getBean(JumpBackConfiguration.class);
            JumpBackConfigService configService = applicationContext.getBean(JumpBackConfigService.class);
            JumpBackExecutionService executionService = applicationContext.getBean(JumpBackExecutionService.class);

            assertNotNull(configuration);
            assertNotNull(configService);
            assertNotNull(executionService);
        }, "基础回跳相关的Bean都应该能正常创建，没有循环依赖问题");
    }
}
