package com.wflow.utils;

import cn.dev33.satoken.stp.StpUtil;
import com.wflow.bean.do_.UserDo;
import com.wflow.service.OrgRepositoryService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Optional;

/**
 * <AUTHOR> willian fu
 * @date : 2022/9/29
 */
public class UserUtil {

    /**
     * 获取当前登录用户的id
     * @return 用户ID
     */
    @Resource
    private OrgRepositoryService orgRepositoryService;

    public static String getLoginUserId(){
        return StpUtil.getLoginIdAsString();
    }

    /**
     * 获取当前用户租户ID
     * @return 租户ID
     */
    public static String getLoginTenantId(){
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        return Optional.ofNullable(request.getHeader("TenantId")).orElse("default");
    }

}
