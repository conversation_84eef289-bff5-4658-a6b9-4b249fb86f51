package com.bjcj.xxgk.mapper.gk;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bjcj.xxgk.model.pojo.gk.GkList;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 征地信息公开列表 Mapper 接口
 * 
 * <AUTHOR> Assistant
 */
public interface GkListMapper extends BaseMapper<GkList> {

    /**
     * 根据行政区编码列表分页查询征地信息公开列表
     * 
     * @param page           分页参数
     * @param districtCodes  行政区编码列表
     * @param district       所属区（可选）
     * @param street         街道（可选）
     * @param unit           单位（可选）
     * @param approvalNumber 批准文号（可选）
     * @return 分页结果
     */
    IPage<GkList> selectPageByDistrictCodes(
            IPage<GkList> page,
            @Param("districtCodes") List<String> districtCodes,
            @Param("district") String district,
            @Param("street") String street,
            @Param("unit") String unit,
            @Param("approvalNumber") String approvalNumber,
            @Param("type") String type
            );
}
