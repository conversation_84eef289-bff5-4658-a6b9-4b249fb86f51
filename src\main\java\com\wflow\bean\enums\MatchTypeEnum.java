package com.wflow.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 匹配类型枚举
 * <AUTHOR> willian fu
 * @date : 2025/01/04
 */
@Getter
@AllArgsConstructor
public enum MatchTypeEnum {
    
    /**
     * 精确匹配
     */
    EXACT("EXACT", "精确匹配"),
    
    /**
     * 包含匹配
     */
    CONTAINS("CONTAINS", "包含匹配"),
    
    /**
     * 正则表达式匹配
     */
    REGEX("REGEX", "正则表达式匹配"),
    
    /**
     * 数值范围匹配
     */
    RANGE("RANGE", "数值范围匹配"),
    
    /**
     * 大于
     */
    GREATER_THAN("GREATER_THAN", "大于"),
    
    /**
     * 小于
     */
    LESS_THAN("LESS_THAN", "小于"),
    
    /**
     * 等于
     */
    EQUALS("EQUALS", "等于"),
    /**
     * 求和
     */
    SUM("SUM", "求和"),
    /**
     * 不等于
     */
    NOT_EQUALS("NOT_EQUALS", "不等于");

    private final String code;
    private final String desc;

    /**
     * 根据代码获取枚举
     */
    public static MatchTypeEnum getByCode(String code) {
        for (MatchTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return EXACT; // 默认返回精确匹配
    }
}
